name: legacy_pms
description: "A new Flutter project."

publish_to: 'none' 


version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # Core Flutter & UI
  cupertino_icons: ^1.0.6
  flashy_flushbar: ^1.4.0
  flutter_date_range_picker: ^0.2.1
  flutter_cupertino_datetime_picker: ^3.0.0
  flutter_image_compress: ^2.4.0
  flutter_pdfview: ^1.3.4
  flutter_staggered_grid_view: ^0.7.0
  fluttertoast: ^8.2.4
  get: ^4.6.6
  get_storage: ^2.1.1
  go_router: ^16.0.0
  google_fonts: ^6.2.1
  shared_preferences: ^2.0.15

  # Firebase
  cloud_firestore: ^6.0.0
  cloud_functions: ^6.0.0
  firebase_auth: ^6.0.1
  firebase_core: ^4.0.0
  firebase_storage: ^13.0.0
  firebase_ui_auth: ^3.0.0

  # File & Media
  csv: ^6.0.0
  excel: ^4.0.6
  file_picker: ^10.1.1
  file_saver: ^0.3.1
  image_picker: ^1.0.7
  path_provider: ^2.1.5
  pdf: ^3.6.0
  pdf_image_renderer: ^1.0.1
  permission_handler: ^12.0.0+1
  qr_flutter: ^4.0.0
  signature: ^6.0.0

  # Utils
  dropdown_search: ^6.0.1
  multi_dropdown: ^3.0.1
  http: ^1.2.2
  intl: ^0.19.0
  url_launcher: ^6.3.1
  duration_picker: ^1.2.0


  # Grids & Tables
  pluto_grid: ^8.0.0
  syncfusion_flutter_datagrid: ^30.1.37
  # pluto_grid:
  #   git:
  #     url: https://github.com/Mohammadwabeel/pluto_grid.git

dependency_overrides:
  intl: 0.20.2

dev_dependencies:
  flutter_lints: ^6.0.0
  flutter_test:
    sdk: flutter



flutter:
  uses-material-design: true

  assets:
    - assets/


