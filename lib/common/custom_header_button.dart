import 'package:flutter/material.dart';
import 'package:legacy_pms/shared/const.dart';

class CustomHeaderButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String buttonName;

  const CustomHeaderButton({
    super.key,
    required this.onPressed,
    required this.buttonName,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: logoTealColor,
          foregroundColor: Colors.white,
          elevation: 2, // a bit more to improve depth
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              8,
            ), // subtle rounding for modern feel
          ),
          padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 14),
          minimumSize: const Size(110, 44),
          shadowColor: logoTealColor.withOpacity(0.3),
          textStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w700,
            letterSpacing: 0.7,
          ),
        ),
        onPressed: onPressed,
        child: Text(buttonName),
      ),
    );
  }
}
