import 'package:flutter/material.dart';
import 'package:legacy_pms/shared/const.dart';

class CustomDialog {
  /// Displays a dialog with the given title, message, and button labels.
  static Future<void> show(
    BuildContext context, {
    required String title,
    required String message,
    String positiveButtonText = "OK",
    String? negativeButtonText,
    VoidCallback? onPositivePressed,
    VoidCallback? onNegativePressed,
    bool barrierDismissible = true,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: popupBgColor,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.zero, // Sharp corners like your button
          ),
          title: Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
          ),
          content: Text(message, style: const TextStyle(fontSize: 16)),
          actionsPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          actions: <Widget>[
            if (negativeButtonText != null)
              TextButton(
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                  if (onNegativePressed != null) onNegativePressed();
                },
                child: Text(
                  negativeButtonText,
                  style: TextStyle(color: logoTealColor),
                ),
              ),
            TextButton(
              style: TextButton.styleFrom(
                backgroundColor: logoTealColor,
                foregroundColor: Colors.white,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.zero,
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              onPressed: () {
                Navigator.of(dialogContext).pop();
                if (onPositivePressed != null) onPositivePressed();
              },
              child: Text(positiveButtonText),
            ),
          ],
        );
      },
    );
  }
}
