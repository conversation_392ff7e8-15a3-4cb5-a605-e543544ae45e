import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class CommonSearchBar extends StatelessWidget {
  const CommonSearchBar({
    super.key,
    required this.searchController,
    required this.searchOnChanged,
  });

  final TextEditingController? searchController;
  final Function(String p1)? searchOnChanged;

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: 320),
      child: SizedB<PERSON>(
        height: 48, // slightly taller for better touch comfort
        child: TextForm<PERSON>ield(
          controller: searchController,
          cursorHeight: 22,
          onChanged: searchOnChanged,
          decoration: InputDecoration(
            filled: true,
            fillColor: Colors
                .grey
                .shade200, // permanent grey background like hover color
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 14,
              vertical: 14,
            ),
            prefixIcon: const Icon(
              CupertinoIcons.search,
              size: 22,
              color: Colors.grey,
            ),
            suffixIcon:
                searchController != null && searchController!.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear, color: Colors.grey, size: 20),
                    onPressed: () {
                      searchController!.clear();
                      if (searchOnChanged != null) searchOnChanged!('');
                    },
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            // focusedBorder: OutlineInputBorder(
            //   borderRadius: BorderRadius.circular(12),
            //   borderSide: BorderSide(
            //     color: Theme.of(context).primaryColor,
            //     width: 2,
            //   ),
            // ),
            hintText: 'Search',
            hintStyle: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey),
          ),
        ),
      ),
    );
  }
}
