import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class HeaderTxt extends StatelessWidget {
  const HeaderTxt({super.key, required this.txt, this.overflow, this.twice});
  final String txt;
  final TextOverflow? overflow;
  final bool? twice;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: (twice ?? false) ? 2 : 1,
      child: Text(
        txt,
        style: TextStyle(fontWeight: FontWeight.w600, overflow: overflow),
      ),
    );
  }
}
