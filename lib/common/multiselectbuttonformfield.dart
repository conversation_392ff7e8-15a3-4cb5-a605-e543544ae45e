import 'package:flutter/material.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

class MultiSelectButtonFormField extends FormField<List<String>> {
  MultiSelectButtonFormField({
    super.key,
    required List<DropdownItem<String>> items,
    required List<String> super.initialValue,
    required void Function(List<String>) onChanged,
    String labelText = '',
    InputDecoration? decoration,
    ChipDecoration chipDecoration = const ChipDecoration(),
    bool enabled = true,
    super.validator,
  }) : super(
         builder: (FormFieldState<List<String>> state) {
           void showMultiSelectDialog(BuildContext context) async {
             final selectedValues = List<String>.from(state.value ?? []);
             final result = await showDialog<List<String>>(
               context: context,
               builder: (context) {
                 List<String> tempSelected = List.from(selectedValues);
                 TextEditingController searchCtrl = TextEditingController();
                 List<DropdownItem<String>> filteredItems = items;

                 return StatefulBuilder(
                   builder: (context, setDialogState) {
                     void filterItems(String query) {
                       setDialogState(() {
                         filteredItems = items
                             .where(
                               (item) => item.label.toLowerCase().contains(
                                 query.toLowerCase(),
                               ),
                             )
                             .toList();
                       });
                     }

                     return AlertDialog(
                       backgroundColor: popupBgColor,
                       title: Text('Select $labelText'),
                       content: Column(
                         mainAxisSize: MainAxisSize.min,
                         children: [
                           TextField(
                             controller: searchCtrl,
                             decoration: const InputDecoration(
                               labelText: 'Search',
                               prefixIcon: Icon(Icons.search),
                             ),
                             onChanged: filterItems,
                           ),
                           const SizedBox(height: 10),
                           Container(
                             constraints: BoxConstraints(
                               maxWidth: 300,
                               maxHeight: 200,
                             ),
                             height: 200,
                             width: double.maxFinite,
                             child: ListView.builder(
                               itemCount: filteredItems.length,
                               itemBuilder: (context, index) {
                                 final item = filteredItems[index];
                                 final checked = tempSelected.contains(
                                   item.value,
                                 );
                                 return CheckboxListTile(
                                   value: checked,
                                   title: Text(item.label),
                                   onChanged: (bool? value) {
                                     if (value == true) {
                                       tempSelected.add(item.value);
                                     } else {
                                       tempSelected.remove(item.value);
                                     }
                                     setDialogState(() {});
                                   },
                                 );
                               },
                             ),
                           ),
                         ],
                       ),
                       actions: [
                         TextButton(
                           child: const Text('Cancel'),
                           onPressed: () => Navigator.pop(context, null),
                         ),
                         ElevatedButton(
                           child: const Text('OK'),
                           onPressed: () =>
                               Navigator.pop(context, tempSelected),
                         ),
                       ],
                     );
                   },
                 );
               },
             );

             if (result != null) {
               state.didChange(result);
               onChanged(result);
             }
           }

           return InkWell(
             onTap: enabled ? () => showMultiSelectDialog(state.context) : null,
             child: InputDecorator(
               decoration:
                   decoration ??
                   InputDecoration(
                     labelText: labelText,
                     errorText: state.errorText,
                     border: const OutlineInputBorder(),
                     contentPadding: const EdgeInsets.symmetric(
                       horizontal: 12,
                       vertical: 16,
                     ),
                   ),
               isEmpty: state.value == null || state.value!.isEmpty,
               child: state.value == null || state.value!.isEmpty
                   ? Text(
                       'Select $labelText',
                       style: TextStyle(
                         color: Theme.of(state.context).hintColor,
                       ),
                     )
                   : Wrap(
                       spacing: 6.0,
                       runSpacing: 5.0,
                       children: state.value!.map((val) {
                         final item = items.firstWhere(
                           (item) => item.value == val,
                           orElse: () => DropdownItem(value: val, label: val),
                         );
                         return Chip(
                           label: Text(item.label),
                           onDeleted: enabled
                               ? () {
                                   final newList = List<String>.from(
                                     state.value!,
                                   );
                                   newList.remove(val);
                                   state.didChange(newList);
                                   onChanged(newList);
                                 }
                               : null,
                           backgroundColor:
                               chipDecoration.backgroundColor ??
                               Colors.grey.shade300,
                           labelStyle:
                               chipDecoration.labelStyle ??
                               const TextStyle(color: Colors.black),
                         );
                       }).toList(),
                     ),
             ),
           );
         },
       );
}
