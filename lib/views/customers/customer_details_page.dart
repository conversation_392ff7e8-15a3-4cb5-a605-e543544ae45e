import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:legacy_pms/models/customermodel.dart';
import 'package:legacy_pms/models/mastertaskmodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/firebase.dart';
import 'package:legacy_pms/shared/methods.dart';

import '../../controller/homectrl.dart';

class CustomerDetailsPage extends StatefulWidget {
  final String? customerId;

  const CustomerDetailsPage({super.key, this.customerId});

  @override
  State<CustomerDetailsPage> createState() => _CustomerDetailsPageState();
}

class _CustomerDetailsPageState extends State<CustomerDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController tabCtrl;
  final HomeCtrl ctrl = Get.find();
  Customermodel? customer;
  bool isLoading = true;
  bool isEditing = false;
  bool isSaving = false;

  late TextEditingController nameController;
  late TextEditingController companyController;
  late TextEditingController emailController;
  late TextEditingController phoneController;
  String? selectedClientType;
  String? selectedClientTimezone;
  String? selectedStatus;
  List<String> selectedMasterTasks = [];
  List<Mastertaskmodel> availableMasterTasks = [];

  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    tabCtrl = TabController(length: 2, vsync: this);
    tabCtrl.addListener(() {
      if (!tabCtrl.indexIsChanging) {
        setState(() {
          _currentTabIndex = tabCtrl.index;
        });
      }
    });
    initializeCtrl();
    loadCustomerData();
  }

  void initializeCtrl() {
    nameController = TextEditingController();
    companyController = TextEditingController();
    emailController = TextEditingController();
    phoneController = TextEditingController();
  }

  Future<void> loadCustomerData() async {
    if (widget.customerId == null) {
      setState(() {
        isLoading = false;
      });
      return;
    }
    try {
      final customerDoc = await FBFireStore.customers
          .doc(widget.customerId)
          .get();
      if (customerDoc.exists) {
        setState(() {
          customer = Customermodel.fromSnap(customerDoc);
          _populateControllers();
          isLoading = false;
        });
      } else {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      showSnackBar('Failed to load customer data: $e');
    }
  }

  void _populateControllers() {
    if (customer != null) {
      nameController.text = customer!.name;
      companyController.text = customer!.companyName;
      emailController.text = customer!.email ?? '';
      phoneController.text = customer!.phoneNo ?? '';
      selectedClientType = customer?.clientType;
      selectedClientTimezone = customer?.timeZone ?? "";
      // selectedClientType = clientType.contains(customer!.clientType)
      //     ? customer!.clientType
      //     : null;
      selectedStatus =
          ['active', 'inactive', 'pending'].contains(customer!.status)
          ? customer!.status
          : null;
      selectedMasterTasks = List<String>.from(customer?.masterTasks ?? []);
    }
  }

  void _toggleEdit() {
    setState(() {
      isEditing = !isEditing;
    });
  }

  Future<void> _saveChanges() async {
    if (customer == null) return;
    setState(() {
      isSaving = true;
    });
    try {
      final updatedData = {
        'name': nameController.text.trim(),
        'companyName': companyController.text.trim(),
        'email': emailController.text.trim().isEmpty
            ? null
            : emailController.text.trim(),
        'phoneNo': phoneController.text.trim().isEmpty
            ? null
            : phoneController.text.trim(),
        'clientType': selectedClientType,
        'timeZone': selectedClientTimezone,
        'status': selectedStatus,
        'masterTasks': selectedMasterTasks,
      };
      await FBFireStore.customers.doc(customer!.docId).update(updatedData);
      setState(() {
        isEditing = false;
        isSaving = false;
      });
      showSnackBar('Customer updated successfully');
      loadCustomerData();
    } catch (e) {
      setState(() {
        isSaving = false;
      });
      showSnackBar('Failed to update customer: $e');
    }
  }

  @override
  void dispose() {
    tabCtrl.dispose();
    nameController.dispose();
    companyController.dispose();
    emailController.dispose();
    phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: isLoading
              ? const Center(child: CircularProgressIndicator())
              : customer == null
              ? _buildCustomerNotFound()
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildTabBar(),
                    const SizedBox(height: 12),
                    Expanded(
                      child: TabBarView(
                        controller: tabCtrl,
                        children: [_buildDetailsTab(), _buildTestTab2()],
                      ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildCustomerNotFound() {
    return Center(
      child: Card(
        elevation: 3,
        margin: const EdgeInsets.symmetric(horizontal: 40),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(28),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.person_off, size: 64, color: Colors.grey),
              const SizedBox(height: 24),
              Text(
                'Customer not found',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.grey[800],
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.pop(),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 28,
                    vertical: 14,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Go Back', style: TextStyle(fontSize: 16)),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: TabBar(
              dividerHeight: 0,
              dividerColor: Colors.transparent,
              controller: tabCtrl,
              indicatorColor: logoTealColor,
              labelColor: logoTealColor,
              unselectedLabelColor: Colors.grey.shade600,
              // indicatorWeight: 1,
              labelStyle: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              tabs: const [
                Tab(text: 'Details'),
                Tab(text: 'Test Tab'),
              ],
            ),
          ),
          const SizedBox(width: 12),

          AnimatedSwitcher(
            duration: const Duration(milliseconds: 250),
            transitionBuilder: (child, animation) =>
                ScaleTransition(scale: animation, child: child),
            child: (_currentTabIndex == 0)
                ? !isEditing
                      ? IconButton(
                          key: const ValueKey('edit'),
                          onPressed:
                              ctrl.userRoles.contains(
                                Permissions.canEditCustomer,
                              )
                              ? () => _toggleEdit()
                              : () {},
                          // _toggleEdit,
                          icon: Icon(Icons.edit, color: Colors.teal[700]),
                          tooltip: 'Edit Customer',
                          splashRadius: 26,
                        )
                      : Row(
                          key: const ValueKey('edit_actions'),
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (isSaving)
                              const SizedBox(
                                width: 26,
                                height: 26,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.teal,
                                  ),
                                ),
                              )
                            else
                              IconButton(
                                onPressed: _saveChanges,
                                icon: Icon(Icons.save, color: Colors.teal[700]),
                                tooltip: 'Save Changes',
                                splashRadius: 26,
                              ),
                            IconButton(
                              onPressed: _toggleEdit,
                              icon: Icon(Icons.close, color: Colors.grey[600]),
                              tooltip: 'Cancel Edit',
                              splashRadius: 26,
                            ),
                          ],
                        )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow(
            "Customer Name",
            nameController,
            isEditing: isEditing,
          ),
          _divider(),
          _buildDetailRow("Email", emailController, isEditing: false),
          _divider(),
          _buildDetailRow(
            "Company Name",
            companyController,
            isEditing: isEditing,
          ),
          _divider(),
          _buildDetailRow("Phone", phoneController, isEditing: isEditing),
          _divider(),
          _buildDropdownRow(
            "Client Type",
            selectedClientType,
            CustomerType.all,
            (val) => setState(() => selectedClientType = val),
            isEditing: isEditing,
          ),
          _divider(),
          _buildDropdownRow(
            "Client Timezone",
            selectedClientTimezone,
            CustomerTimezone.all,
            (val) => setState(() => selectedClientTimezone = val),
            isEditing: isEditing,
          ),
          _divider(),
          _buildDropdownRow(
            "Status",
            selectedStatus,
            ['active', 'inactive', 'pending'],
            (val) => setState(() => selectedStatus = val),
            isEditing: isEditing,
          ),
          _divider(),
          _buildMasterTasksRow(),
        ],
      ),
    );
  }

  Widget _divider() => Divider(color: Colors.grey.shade300, height: 32);

  Widget _buildDetailRow(
    String label,
    TextEditingController controller, {
    bool isEditing = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey,
            ),
          ),
        ),
        isEditing
            ? SizedBox(
                width: 300,
                // height: 56,
                child: TextField(
                  controller: controller,
                  decoration: InputDecoration(
                    hintText: "Enter $label",
                    isDense: true,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 10,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: Colors.teal.shade400,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  style: const TextStyle(fontSize: 16),
                ),
              )
            : Expanded(
                child: Text(
                  controller.text.isEmpty ? 'Not specified' : controller.text,
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                ),
              ),
      ],
    );
  }

  Widget _buildDropdownRow(
    String label,
    String? value,
    List<String> options,
    ValueChanged<String?> onChanged, {
    bool isEditing = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey,
            ),
          ),
        ),
        isEditing
            ? SizedBox(
                width: 300,
                child: DropdownButtonFormField<String>(
                  value: value,
                  decoration: InputDecoration(
                    isDense: true,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 10,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  items: options.map((String option) {
                    return DropdownMenuItem<String>(
                      value: option,
                      child: Text(option),
                    );
                  }).toList(),
                  onChanged: onChanged,
                ),
              )
            : Expanded(
                child: Text(
                  value ?? 'Not specified',
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                ),
              ),
      ],
    );
  }

  Widget _buildMasterTasksRow() {
    return FutureBuilder<List<Mastertaskmodel>>(
      future: FBFireStore.masterTasks.get().then(
        (snapshot) =>
            snapshot.docs.map((doc) => Mastertaskmodel.fromSnap(doc)).toList(),
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Row(
            children: [
              SizedBox(
                width: 120,
                child: Text(
                  "Master Tasks",
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.grey,
                  ),
                ),
              ),
              Expanded(child: CircularProgressIndicator()),
            ],
          );
        }

        if (snapshot.hasError) {
          return Row(
            children: [
              SizedBox(
                width: 120,
                child: Text(
                  "Master Tasks",
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.grey,
                  ),
                ),
              ),
              Expanded(
                child: Text('Error loading master tasks: ${snapshot.error}'),
              ),
            ],
          );
        }

        if (snapshot.hasData) {
          availableMasterTasks = snapshot.data!;
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    "Master Tasks",
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.grey,
                    ),
                  ),
                ),
                Expanded(
                  child: isEditing
                      ? _buildMasterTasksEditSection()
                      : _buildMasterTasksDisplaySection(),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildMasterTasksDisplaySection() {
    if (selectedMasterTasks.isEmpty) {
      return const Text(
        'No master tasks assigned',
        style: TextStyle(fontSize: 16, color: Colors.grey),
      );
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: selectedMasterTasks.map((taskId) {
        final task = availableMasterTasks.isNotEmpty
            ? availableMasterTasks.firstWhere(
                (t) => t.docId == taskId,
                orElse: () => Mastertaskmodel(
                  docId: taskId,
                  title: 'Unknown Task',
                  desc: '',
                  createdAt: DateTime.now(),
                  type: '',
                  benchmark: '',
                  showCount: false,
                  masterQc: '',
                ),
              )
            : Mastertaskmodel(
                docId: taskId,
                title: 'Unknown Task',
                desc: '',
                createdAt: DateTime.now(),
                type: '',
                benchmark: '',
                showCount: false,
                masterQc: '',
              );
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: logoTealColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: logoTealColor.withOpacity(0.3)),
          ),
          child: Text(
            task.title,
            style: TextStyle(
              fontSize: 14,
              color: logoTealColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildMasterTasksEditSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display selected tasks
        if (selectedMasterTasks.isNotEmpty) ...[
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: selectedMasterTasks.map((taskId) {
              final task = availableMasterTasks.isNotEmpty
                  ? availableMasterTasks.firstWhere(
                      (t) => t.docId == taskId,
                      orElse: () => Mastertaskmodel(
                        docId: taskId,
                        title: 'Unknown Task',
                        desc: '',
                        createdAt: DateTime.now(),
                        type: '',
                        benchmark: '',
                        showCount: false,
                        masterQc: '',
                      ),
                    )
                  : Mastertaskmodel(
                      docId: taskId,
                      title: 'Unknown Task',
                      desc: '',
                      createdAt: DateTime.now(),
                      type: '',
                      benchmark: '',
                      showCount: false,
                      masterQc: '',
                    );
              return Chip(
                label: Text(task.title, style: const TextStyle(fontSize: 12)),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: () {
                  setState(() {
                    selectedMasterTasks.remove(taskId);
                  });
                },
                backgroundColor: logoTealColor.withOpacity(0.1),
                deleteIconColor: logoTealColor,
                labelStyle: TextStyle(color: logoTealColor),
              );
            }).toList(),
          ),
          const SizedBox(height: 12),
        ],
        // Dropdown for adding tasks
        if (availableMasterTasks.isNotEmpty) ...[
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: "Add Master Task",
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.add),
            ),
            value: null, // Always null to show placeholder
            items: availableMasterTasks
                .where((task) => !selectedMasterTasks.contains(task.docId))
                .map(
                  (task) => DropdownMenuItem(
                    value: task.docId,
                    child: Text(task.title),
                  ),
                )
                .toList(),
            onChanged: (String? taskId) {
              if (taskId != null) {
                setState(() {
                  selectedMasterTasks.add(taskId);
                });
              }
            },
            hint: const Text('Select a master task to add'),
          ),
        ] else ...[
          const Text(
            'No master tasks available',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ],
    );
  }

  Widget _buildTestTab2() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.construction, size: 64, color: Colors.grey[500]),
          const SizedBox(height: 16),
          const Text(
            'Test Tab 2',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'This tab is reserved for future functionality',
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }
}
