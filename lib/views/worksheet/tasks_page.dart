import 'dart:async';
import 'dart:io';
import 'package:excel/excel.dart' as excel;
import 'package:file_saver/file_saver.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:duration_picker/duration_picker.dart';
import 'package:legacy_pms/common/common_searchbar.dart';
import 'package:legacy_pms/common/custom_header_button.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/models/customermodel.dart';
import 'package:legacy_pms/models/masterqcmodel.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:legacy_pms/views/worksheet/qcform_dialog.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pluto_grid/pluto_grid.dart';
import 'package:legacy_pms/models/taskmodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/firebase.dart';

class TasksPage extends StatefulWidget {
  const TasksPage({super.key});

  @override
  State<TasksPage> createState() => _TasksPageState();
}

class _TasksPageState extends State<TasksPage> {
  List<TaskModel> tasks = [];

  bool isLoading = true;

  bool isQcSubmitted = true;

  PlutoGridStateManager? gridStateManager;

  Timer? _debounce;

  bool isExporting = false;

  int parseHoursToMinutes(String? hoursStr) {
    if (hoursStr == null || hoursStr.trim().isEmpty) return 0;

    try {
      final parts = hoursStr.contains(':')
          ? hoursStr.split(':')
          : [hoursStr.substring(0, 2), hoursStr.substring(2)];

      final hours = int.parse(parts[0]);
      final minutes = int.parse(parts[1]);
      return hours * 60 + minutes;
    } catch (e) {
      return 0; // Invalid format
    }
  }

  bool _isNewTaskValid(TaskModel task) {
    final totalMinutes = parseHoursToMinutes(task.actualHours);
    return task.customer.isNotEmpty &&
        task.employee.isNotEmpty &&
        task.supervisor.isNotEmpty &&
        task.details.isNotEmpty &&
        // (task.actualHours?.toString().isNotEmpty ?? false) &&
        totalMinutes > 0 &&
        task.pType != null &&
        task.status != null &&
        task.status!.isNotEmpty;
  }

  @override
  void initState() {
    super.initState();
    _loadTasks();
  }

  Future<void> _loadTasks() async {
    setState(() => isLoading = true);

    try {
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = startOfDay
          .add(Duration(days: 1))
          .subtract(Duration(milliseconds: 1));

      final snapshot = await FBFireStore.tasks
          .where('createdAt', isGreaterThanOrEqualTo: startOfDay)
          .where('createdAt', isLessThanOrEqualTo: endOfDay)
          .get();

      final loadedTasks = snapshot.docs
          .map((doc) => TaskModel.fromSnap(doc))
          .toList();

      loadedTasks.sort(
        (a, b) => a.createdAt.compareTo(b.createdAt),
      ); // FIFO sorting here

      setState(() {
        tasks = loadedTasks;
        isLoading = false;
      });

      // Also refresh PlutoGrid rows if it is loaded
      if (mounted) {
        final ctrl = Get.find<HomeCtrl>();

        // Filter tasks based on permissions
        List<TaskModel> userFilteredTasks;
        if (ctrl.userRoles.contains(Permissions.canSeeAllTasks)) {
          userFilteredTasks = tasks;
        } else {
          final currentUserUid = ctrl.loggedInUser?.docId;
          userFilteredTasks = tasks
              .where(
                (task) =>
                    task.employee == currentUserUid ||
                    task.supervisor == currentUserUid,
              )
              .toList();
        }

        // Apply search filter (use current searchctrl text)
        final query = searchctrl.text.toLowerCase();
        final filteredTasks = query.isEmpty
            ? userFilteredTasks
            : userFilteredTasks.where((task) {
                final activity =
                    ctrl.masterTasks
                        .firstWhereOrNull((a) => a.docId == task.pType)
                        ?.title
                        .toLowerCase() ??
                    '';
                final details = (task.details).toLowerCase();
                final employee =
                    ctrl.users
                        .firstWhereOrNull((u) => u.docId == task.employee)
                        ?.name
                        .toLowerCase() ??
                    '';
                final supervisor =
                    ctrl.users
                        .firstWhereOrNull((u) => u.docId == task.supervisor)
                        ?.name
                        .toLowerCase() ??
                    '';
                return activity.contains(query) ||
                    details.contains(query) ||
                    employee.contains(query) ||
                    supervisor.contains(query);
              }).toList();

        final filteredRows = _buildRowsFromTasks(filteredTasks, ctrl);
        gridStateManager?.removeAllRows(notify: false);
        gridStateManager?.appendRows(filteredRows);
        gridStateManager?.notifyListeners();
      }
    } catch (e) {
      setState(() => isLoading = false);
      showSnackBar('Failed to load tasks: $e');
    }
  }

  // void _showEditTaskDialog(TaskModel task) {
  //   showDialog(
  //     context: context,
  //     barrierDismissible: false,
  //     builder: (context) => AddTaskDialog(task: task),
  //   ).then((_) => _loadTasks());
  // }

  Future<void> _confirmDeleteTask(TaskModel task) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: popupBgColor,
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this task?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            // style: ElevatedButton.styleFrom(backgroundColor: redColor),
            child: const Text('Yes'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await FBFireStore.tasks.doc(task.docId).delete();
        showSnackBar('Task deleted successfully');
        _loadTasks();
      } catch (e) {
        showSnackBar('Failed to delete task: $e');
      }
    }
  }

  SearchController searchctrl = SearchController();

  TaskModel? newTaskBeingAdded;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final query = searchctrl.text.toLowerCase();
        final currentUser = ctrl.loggedInUser;
        final currentUserUid = currentUser?.docId;

        // Filter tasks based on permissions
        List<TaskModel> userFilteredTasks;
        if (ctrl.userRoles.contains(Permissions.canSeeAllTasks)) {
          // User can see all tasks
          userFilteredTasks = tasks;
        } else {
          // User can only see their own tasks
          userFilteredTasks = tasks
              .where(
                (task) =>
                    // task.customer == currentUserUid ||
                    task.employee == currentUserUid ||
                    task.supervisor == currentUserUid,
              )
              .toList();
        }

        // Apply search filter on the user-filtered tasks
        final filteredTasks = query.isEmpty
            ? userFilteredTasks
            : userFilteredTasks.where((task) {
                // final customer =
                //     ctrl.customers
                //         .firstWhereOrNull((c) => c.docId == task.customer)
                //         ?.name
                //         .toLowerCase() ??
                //     '';
                final activity =
                    ctrl.masterTasks
                        .firstWhereOrNull((a) => a.docId == task.pType)
                        ?.title
                        .toLowerCase() ??
                    '';
                final details = (task.details).toLowerCase();
                final employee =
                    ctrl.users
                        .firstWhereOrNull((u) => u.docId == task.employee)
                        ?.name
                        .toLowerCase() ??
                    '';
                final supervisor =
                    ctrl.users
                        .firstWhereOrNull((u) => u.docId == task.supervisor)
                        ?.name
                        .toLowerCase() ??
                    '';

                return
                // customer.contains(query) ||
                activity.contains(query) ||
                    details.contains(query) ||
                    employee.contains(query) ||
                    supervisor.contains(query);
              }).toList();

        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// HEADER BAR
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        "Tasks (${filteredTasks.length})",
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: Colors.black,
                          letterSpacing: 0.8,
                        ),
                      ),
                      SizedBox(width: 20),
                      CommonSearchBar(
                        searchController: searchctrl,
                        searchOnChanged: (value) {
                          setState(() {
                            final filteredRows = _buildRowsFromTasks(
                              filteredTasks,
                              ctrl,
                            );
                            gridStateManager?.removeAllRows(notify: false);
                            gridStateManager?.appendRows(filteredRows);
                            gridStateManager?.notifyListeners();
                          });
                        },
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      isExporting
                          ? CircularProgressIndicator()
                          : CustomHeaderButton(
                              onPressed:
                                  // ctrl.userRoles.contains(Permissions.canAddTask)
                                  // ? _showAddTaskDialog
                                  // :
                                  () async {
                                    await exportTasksToExcel(
                                      tasks: filteredTasks,
                                      ctrl: ctrl,
                                      context: context,
                                    );
                                  },
                              buttonName: "Export",
                            ),
                      // const SizedBox(width: 10),
                      // Test reminder emails button (for testing purposes)
                      /*   if (ctrl.userRoles.contains(Permissions.canAddTask))
                        CustomHeaderButton(
                          onPressed: () async {
                            showSnackBar('Testing reminder emails...');

                            // Test all reminder types with detailed results
                            final result12pm =
                                await sendTaskReminderEmailsDetailed(
                                  reminderType: '12pm',
                                );
                            final result3pm =
                                await sendTaskReminderEmailsDetailed(
                                  reminderType: '3pm',
                                );
                            final result430pm =
                                await sendTaskReminderEmailsDetailed(
                                  reminderType: '4:30pm',
                                );

                            // Calculate total emails sent
                            final totalEmails =
                                (result12pm['emailsSent'] ?? 0) +
                                (result3pm['emailsSent'] ?? 0) +
                                (result430pm['emailsSent'] ?? 0);

                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: Text('Reminder Test Results'),
                                content: SizedBox(
                                  width: 500,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Test completed for all reminder types:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      SizedBox(height: 15),

                                      // 12pm results
                                      Text(
                                        '12pm Reminder:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      Text(
                                        '  Status: ${result12pm['success'] ? 'Success' : 'Failed'}',
                                      ),
                                      Text(
                                        '  Emails sent: ${result12pm['emailsSent'] ?? 0}',
                                      ),
                                      if (result12pm['message'] != null)
                                        Text(
                                          '  Message: ${result12pm['message']}',
                                        ),
                                      SizedBox(height: 8),

                                      // 3pm results
                                      Text(
                                        '3pm Reminder:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      Text(
                                        '  Status: ${result3pm['success'] ? 'Success' : 'Failed'}',
                                      ),
                                      Text(
                                        '  Emails sent: ${result3pm['emailsSent'] ?? 0}',
                                      ),
                                      if (result3pm['message'] != null)
                                        Text(
                                          '  Message: ${result3pm['message']}',
                                        ),
                                      SizedBox(height: 8),

                                      // 4:30pm results
                                      Text(
                                        '4:30pm Reminder:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      Text(
                                        '  Status: ${result430pm['success'] ? 'Success' : 'Failed'}',
                                      ),
                                      Text(
                                        '  Emails sent: ${result430pm['emailsSent'] ?? 0}',
                                      ),
                                      if (result430pm['message'] != null)
                                        Text(
                                          '  Message: ${result430pm['message']}',
                                        ),
                                      SizedBox(height: 15),

                                      // Summary
                                      Container(
                                        padding: EdgeInsets.all(10),
                                        decoration: BoxDecoration(
                                          color: Colors.grey[100],
                                          borderRadius: BorderRadius.circular(
                                            5,
                                          ),
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Summary:',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            Text(
                                              'Total emails sent: $totalEmails',
                                            ),
                                            if (totalEmails == 0)
                                              Text(
                                                'No emails were sent. This may be because there are no tasks created today that match the reminder criteria.',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  fontStyle: FontStyle.italic,
                                                  color: Colors.orange[700],
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(),
                                    child: Text('OK'),
                                  ),
                                ],
                              ),
                            );
                          },
                          buttonName: "Test Reminders",
                        ), */
                      const SizedBox(width: 10),

                      //Add task button
                      CustomHeaderButton(
                        onPressed:
                            ctrl.userRoles.contains(Permissions.canAddTask)
                            ? () async {
                                // final result = await FBFunctions.ff
                                //     .httpsCallable('testLoggedInUsersCheck')
                                //     .call();
                                // print("result: ${result.data}");
                                setState(() {
                                  newTaskBeingAdded = TaskModel.empty();
                                  tasks.add(
                                    newTaskBeingAdded!,
                                  ); // append at the end

                                  final rows = _buildRowsFromTasks(tasks, ctrl);
                                  gridStateManager?.removeAllRows();
                                  gridStateManager?.appendRows(rows);
                                  gridStateManager?.notifyListeners();
                                });
                              }
                            : () {},
                        buttonName: "Add Task",
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 20),

              /// CONTENT (Grid or Empty state)
              Expanded(
                child: isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : filteredTasks.isEmpty
                    ? _buildEmptyState()
                    : PlutoGrid(
                        columns: _buildColumns(ctrl),
                        rows: _buildRowsFromTasks(filteredTasks, ctrl),
                        onLoaded: (event) {
                          gridStateManager = event.stateManager;
                        },
                        onChanged: (event) {
                          _handleCellEdit(event, ctrl);
                        },
                        configuration: PlutoGridConfiguration(
                          style: PlutoGridStyleConfig(
                            borderColor: Colors.grey.shade300,
                            rowHeight: 45,
                            gridBorderColor: Colors.grey.shade200,
                          ),
                          scrollbar: PlutoGridScrollbarConfig(
                            draggableScrollbar: true,
                            isAlwaysShown: true,
                            scrollbarThickness:
                                PlutoScrollbar.defaultThicknessWhileDragging,
                          ),
                        ),
                        // configuration: PlutoGridConfiguration(
                        //   style: PlutoGridStyleConfig(
                        //     borderColor: Colors.grey.shade300,
                        //     rowHeight: 45,
                        //     gridBorderColor: Colors.grey.shade200,
                        //   ),
                        // ),
                      ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.task_outlined, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            "No tasks found",
            style: TextStyle(
              fontSize: 20,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            "Tap 'Add Task' to create your first one.",
            style: TextStyle(color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  /// Rows mapping
  List<PlutoRow> _buildRowsFromTasks(
    List<TaskModel> filteredTasks,
    HomeCtrl ctrl,
  ) {
    return filteredTasks.asMap().entries.map((entry) {
      final task = entry.value;
      final index = entry.key;
      final customer = ctrl.customers.firstWhereOrNull(
        (c) => c.docId == task.customer,
      );
      final employee = ctrl.users.firstWhereOrNull(
        (u) => u.docId == task.employee,
      );
      final supervisor = ctrl.users.firstWhereOrNull(
        (u) => u.docId == task.supervisor,
      );
      final activity = ctrl.masterTasks.firstWhereOrNull(
        (a) => a.docId == task.pType,
      );
      final benchmark = activity?.benchmark;

      return PlutoRow(
        cells: {
          'srNo': PlutoCell(value: index + 1),
          'createdAt': PlutoCell(
            value: DateFormat('dd-MM-yyyy').format(task.createdAt),
          ),
          'customer': PlutoCell(value: customer?.name ?? ''),
          'details': PlutoCell(value: task.details),
          'employee': PlutoCell(value: employee?.name ?? ''),
          'supervisor': PlutoCell(value: supervisor?.name ?? ''),
          'pType': PlutoCell(value: activity?.title ?? ''),
          'hours': PlutoCell(value: task.actualHours ?? ''),
          'status': PlutoCell(value: task.status ?? ''),
          'benchmark': PlutoCell(value: benchmark ?? ''),
          'result': PlutoCell(value: task.result ?? ''),
          'actualHours': PlutoCell(value: task.actualHours ?? ''),
          'reportSentStatus': PlutoCell(value: task.reportSentStatus ?? ''),
          'customerCallStatus': PlutoCell(value: task.customerCallStatus ?? ''),
          'callSummary': PlutoCell(value: task.callSummary ?? ''),
          'qcFile': PlutoCell(
            value: getQcTitle(ctrl.masterQcs, task.qcFile) ?? '',
          ),
          'numOfErrors': PlutoCell(
            value: int.tryParse(task.numOfErrors ?? '0') ?? 0,
          ),
          'linksandcomments': PlutoCell(value: task.linksAndComments ?? ''),
          'actions': PlutoCell(value: task),
        },
      );
    }).toList();
  }

  TaskModel _updateTaskModelField(
    TaskModel task,
    String field,
    dynamic value,
    HomeCtrl ctrl,
  ) {
    TaskModel updated = task;
    switch (field) {
      case 'customer':
        final customer = ctrl.customers.firstWhereOrNull(
          (c) => c.name == value,
        );
        if (customer != null)
          updated = updated.copyWith(customer: customer.docId);
        break;
      case 'pType':
        final activity = ctrl.masterTasks.firstWhereOrNull(
          (a) => a.title == value,
        );
        if (activity != null) {
          // Also auto-assign qcFile from selected master task if available
          final String qcId = activity.masterQc;
          updated = updated.copyWith(
            pType: activity.docId,
            qcFile: (qcId.isNotEmpty) ? qcId : updated.qcFile,
          );
        }
        break;
      case 'employee':
        final employee = ctrl.users.firstWhereOrNull((u) => u.name == value);
        if (employee != null)
          updated = updated.copyWith(employee: employee.docId);
        break;
      case 'supervisor':
        final supervisor = ctrl.users.firstWhereOrNull((u) => u.name == value);
        if (supervisor != null)
          updated = updated.copyWith(supervisor: supervisor.docId);
        break;
      case 'details':
        updated = updated.copyWith(details: value);
        break;
      case 'status':
        updated = updated.copyWith(status: value);
        break;
      case 'result':
        updated = updated.copyWith(result: value);
        break;
      case 'actualHours':
        updated = updated.copyWith(actualHours: value);
        break;
      case 'hours': // grid field name maps to actualHours in model
        updated = updated.copyWith(actualHours: value);
        break;
      case 'reportSentStatus':
        updated = updated.copyWith(reportSentStatus: value);
        break;
      case 'customerCallStatus':
        updated = updated.copyWith(customerCallStatus: value);
        break;
      case 'callSummary':
        updated = updated.copyWith(callSummary: value);
        break;
      case 'qcFile':
        updated = updated.copyWith(qcFile: value);
        break;
      case 'numOfErrors':
        updated = updated.copyWith(numOfErrors: value.toString());
        break;
      case 'linksandcomments':
        updated = updated.copyWith(linksAndComments: value);
        break;
    }
    return updated;
  }

  Future<void> _handleCellEdit(
    PlutoGridOnChangedEvent event,
    HomeCtrl ctrl,
  ) async {
    final task = event.row.cells['actions']!.value as TaskModel;
    final field = event.column.field;
    final value = event.value;

    // Update local task fields accordingly here (similar to your switch-case)

    if (task == newTaskBeingAdded) {
      // Update fields of newTaskBeingAdded based on field, value
      final updated = _updateTaskModelField(
        newTaskBeingAdded!,
        field,
        value,
        ctrl,
      );
      // Reflect back to row and local references
      event.row.cells['actions']!.value = updated;
      newTaskBeingAdded = updated;

      // If all required fields are valid, save new task (only if not yet created)
      if (updated.docId.isEmpty && _isNewTaskValid(updated)) {
        try {
          final data = updated.toSnap();
          final docRef = await FBFireStore.tasks.add(data);

          final created = updated.copyWith(docId: docRef.id);

          // Replace in local tasks list
          final idx = tasks.indexWhere((t) => identical(t, task));
          if (idx != -1) {
            tasks[idx] = created;
          }

          // Update grid row 'actions' with created model
          event.row.cells['actions']!.value = created;

          // Clear new row flag
          setState(() {
            newTaskBeingAdded = null;
          });

          // Refresh data to pick up any server timestamps/consistency
          await _loadTasks();

          // Send email notification for new task
          final emailSent = await sendTaskAssignmentEmail(task: created);

          if (emailSent) {
            showSnackBar('New task added and email notifications sent');
          } else {
            showSnackBar('New task added but email notifications failed');
          }
        } catch (e) {
          showSnackBar('Failed to add new task: $e');
        }
      }
    } else {
      // Existing tasks: handle normal update
      // Keep the in-memory model in sync for smoother UI
      final updatedTask = _updateTaskModelField(task, field, value, ctrl);
      event.row.cells['actions']!.value = updatedTask;

      final updateData = _buildUpdateDataForField(field, value, ctrl);
      if (updateData.isNotEmpty) {
        if (_debounce?.isActive ?? false) _debounce!.cancel();
        _debounce = Timer(const Duration(milliseconds: 700), () {
          _updateTaskInFirebase(updatedTask, updateData);
        });
      }
    }
  }

  Map<String, dynamic> _buildUpdateDataForField(
    String field,
    dynamic value,
    HomeCtrl ctrl,
  ) {
    Map<String, dynamic> updateData = {};

    switch (field) {
      case 'customer':
        final customer = ctrl.customers.firstWhereOrNull(
          (c) => c.name == value,
        );
        if (customer != null) updateData['customer'] = customer.docId;
        break;
      case 'pType':
        final activity = ctrl.masterTasks.firstWhereOrNull(
          (a) => a.title == value,
        );
        if (activity != null) {
          updateData['pType'] = activity.docId;
          if (activity.masterQc.isNotEmpty) {
            updateData['qcFile'] = activity.masterQc;
          }
        }
        break;
      case 'employee':
        final employee = ctrl.users.firstWhereOrNull((u) => u.name == value);
        if (employee != null) updateData['employee'] = employee.docId;
        break;
      case 'supervisor':
        final supervisor = ctrl.users.firstWhereOrNull((u) => u.name == value);
        if (supervisor != null) updateData['supervisor'] = supervisor.docId;
        break;
      case 'details':
        updateData['details'] = value;
        break;
      case 'status':
        updateData['status'] = value;
        if ((value as String) == 'Completed') {
          updateData['completed'] = true;
          updateData['completedAt'] = DateTime.now();
        } else {
          updateData['completed'] = false;
          updateData['completedAt'] = null;
        }
        break;
      case 'result':
        updateData['result'] = value;
        break;
      case 'actualHours':
        updateData['actualHours'] = value;
        break;
      case 'hours':
        updateData['actualHours'] = value;
        break;
      case 'reportSentStatus':
        updateData['reportSentStatus'] = value;
        break;
      case 'customerCallStatus':
        updateData['customerCallStatus'] = value;
        break;
      case 'callSummary':
        updateData['callSummary'] = value;
        break;
      case 'qcFile':
        updateData['qcFile'] = value;
        break;
      case 'numOfErrors':
        updateData['numOfErrors'] = value.toString();
        break;
      case 'linksandcomments':
        updateData['linksAndComments'] = value;
        break;
    }

    return updateData;
  }

  // Format HH:MM string
  String _formatDuration(Duration d) {
    final hh = d.inHours.toString().padLeft(2, '0');
    final mm = (d.inMinutes % 60).toString().padLeft(2, '0');
    return '$hh:$mm';
  }

  // Parse HH:MM to Duration
  Duration? _parseHoursString(String value) {
    if (value.isEmpty) return null;
    final parts = value.split(':');
    if (parts.length != 2) return null;
    final hours = int.tryParse(parts[0]) ?? 0;
    final minutes = int.tryParse(parts[1]) ?? 0;
    return Duration(hours: hours, minutes: minutes);
  }

  Future<void> exportTasksToExcel({
    required List<TaskModel> tasks,
    required HomeCtrl ctrl,
    required BuildContext context,
  }) async {
    if (isExporting) return; // Prevent multiple exports

    try {
      setState(() {
        isExporting = true; // Start loader in UI
      });

      print('Starting export of ${tasks.length} tasks');
      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile['Sheet1'];
      print('Created and accessed sheet: ${sheet.sheetName}');

      final headers = [
        excel.TextCellValue('Date'),
        excel.TextCellValue('Customer'),
        excel.TextCellValue('Activity Type'),
        excel.TextCellValue('Tasks'),
        excel.TextCellValue('Employee'),
        excel.TextCellValue('TL'),
        excel.TextCellValue('Status'),
        excel.TextCellValue('Result'),
        excel.TextCellValue('Hours'),
        excel.TextCellValue('Report Sent'),
        excel.TextCellValue('Customer Call'),
        excel.TextCellValue('Call Summary'),
        excel.TextCellValue('QC File'),
        excel.TextCellValue('Errors'),
      ];

      print('Appending header row');
      sheet.appendRow(headers);

      for (final task in tasks) {
        final customer =
            ctrl.customers
                .firstWhereOrNull((c) => c.docId == task.customer)
                ?.name ??
            '';
        final employee =
            ctrl.users
                .firstWhereOrNull((u) => u.docId == task.employee)
                ?.name ??
            '';
        final supervisor =
            ctrl.users
                .firstWhereOrNull((u) => u.docId == task.supervisor)
                ?.name ??
            '';
        final activity =
            ctrl.masterTasks
                .firstWhereOrNull((a) => a.docId == task.pType)
                ?.title ??
            '';

        final qcFileName = getQcTitle(ctrl.masterQcs, task.qcFile) ?? '';
        final dateString = DateFormat('dd-MM-yyyy').format(task.createdAt);

        final row = [
          excel.TextCellValue(dateString),
          excel.TextCellValue(customer),
          excel.TextCellValue(activity),
          excel.TextCellValue(task.details),
          excel.TextCellValue(employee),
          excel.TextCellValue(supervisor),
          excel.TextCellValue(task.status ?? ''),
          excel.TextCellValue(task.result ?? ''),
          excel.TextCellValue(task.actualHours ?? ''),
          excel.TextCellValue(task.reportSentStatus ?? ''),
          excel.TextCellValue(task.customerCallStatus ?? ''),
          excel.TextCellValue(task.callSummary ?? ''),
          excel.TextCellValue(qcFileName),
          excel.TextCellValue(task.numOfErrors ?? ''),
        ];

        sheet.appendRow(row);
        print('Appended row for task ID: ${task.docId}');
      }

      final excelBytes = excelFile.encode();
      print('Excel encoded, byte length: ${excelBytes?.length}');

      final fileName = 'Tasks_${FBAuth.auth.currentUser?.email}.xlsx';

      if (kIsWeb) {
        print('Running on Web - Triggering saveFile');
        await FileSaver.instance.saveFile(
          name: fileName,
          bytes: Uint8List.fromList(excelBytes!),
          fileExtension: 'xlsx',
          mimeType: MimeType.microsoftExcel,
        );
        print('File save invoked on web');
      } else {
        final dir = await getExternalStorageDirectory();
        final filePath = '${dir!.path}/$fileName';
        await File(filePath).writeAsBytes(excelBytes!);
        print('File saved locally at $filePath');
        showSnackBar('Exported to $filePath');
      }

      print('Export completed successfully');
    } catch (e) {
      print('Export failed: $e');
      showSnackBar('Failed to export tasks: $e');
    } finally {
      setState(() {
        isExporting = false; // Stop loader in UI for both success and failure
      });
    }
  }

  Future<void> _updateTaskInFirebase(
    TaskModel task,
    Map<String, dynamic> updateData,
  ) async {
    try {
      await FBFireStore.tasks.doc(task.docId).update(updateData);
      showSnackBar("Task updated successfully");
      await _loadTasks();
    } catch (e) {
      showSnackBar("Failed to update task: $e");
      _loadTasks();
    }
  }

  String? getQcTitle(List<Masterqcmodel> qcs, String? id) {
    if (id == null) return null;
    final qc = qcs.firstWhereOrNull((item) => item.docId == id);
    return qc?.title;
  }

  /// PlutoGrid Columns
  List<PlutoColumn> _buildColumns(HomeCtrl ctrl) {
    return [
      PlutoColumn(
        title: "Date",
        field: "createdAt",
        type: PlutoColumnType.text(),
        // width: 100,
        readOnly: true,
      ),

      PlutoColumn(
        title: "Customer *",
        field: "customer",
        type: PlutoColumnType.text(),
        enableEditingMode: true,
        readOnly: false,

        renderer: (rendererContext) {
          final val = rendererContext.cell.value as String? ?? '';
          final task =
              rendererContext.row.cells['actions']!.value as TaskModel?;
          final isNewRow = task != null && (task.docId.isEmpty);
          final isEmptyRequired = isNewRow && val.isEmpty;

          // Check if this cell is currently being edited
          final isEditing =
              rendererContext.stateManager.isEditing &&
              rendererContext.stateManager.currentCell?.key ==
                  rendererContext.cell.key;

          if (isEditing) {
            return _CustomerEditingCell(
              initialValue: val,
              customers: ctrl.customers,
              onChanged: (value) {
                rendererContext.stateManager.changeCellValue(
                  rendererContext.cell,
                  value,
                  notify: true,
                );
              },
              onEditingComplete: () {
                rendererContext.stateManager.setEditing(false);
              },
            );
          }

          return Container(
            color: isEmptyRequired ? Colors.red.withOpacity(0.2) : null,
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.symmetric(horizontal: 6),
            child: Text(val.isEmpty ? 'Type to search customers...' : val),
          );
        },
      ),

      PlutoColumn(
        title: "Activity Type *",
        field: "pType",
        type: PlutoColumnType.select(
          ctrl.masterTasks.map((a) => a.title).toList(),
        ),
        enableEditingMode: true,
        renderer: (rendererContext) {
          final val = rendererContext.cell.value as String? ?? '';
          final task =
              rendererContext.row.cells['actions']!.value as TaskModel?;
          final isNewRow = task != null && (task.docId.isEmpty);
          final isEmptyRequired = isNewRow && val.isEmpty;
          return Container(
            color: isEmptyRequired ? Colors.red.withOpacity(0.2) : null,
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.symmetric(horizontal: 6),
            child: Text(val),
          );
        },
      ),
      PlutoColumn(
        title: "Tasks *",
        field: "details",
        type: PlutoColumnType.text(),
        // width: 220,
        enableEditingMode: true,
        renderer: (rendererContext) {
          final val = rendererContext.cell.value as String? ?? '';
          final task =
              rendererContext.row.cells['actions']!.value as TaskModel?;
          final isNewRow = task != null && (task.docId.isEmpty);
          final isEmptyRequired = isNewRow && val.isEmpty;
          return Container(
            color: isEmptyRequired ? Colors.red.withOpacity(0.2) : null,
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.symmetric(horizontal: 6),
            child: Text(val),
          );
        },
      ),
      PlutoColumn(
        title: "Employee *",
        field: "employee",
        type: PlutoColumnType.select(
          enableColumnFilter: true,
          ctrl.users.map((u) => u.name).toList(),
        ),
        renderer: (rendererContext) {
          final val = rendererContext.cell.value as String? ?? '';
          final task =
              rendererContext.row.cells['actions']!.value as TaskModel?;
          final isNewRow = task != null && (task.docId.isEmpty);
          final isEmptyRequired = isNewRow && val.isEmpty;
          return Container(
            color: isEmptyRequired ? Colors.red.withOpacity(0.2) : null,
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.symmetric(horizontal: 6),
            child: Text(val),
          );
        },
        // width: 140,
        enableEditingMode: true,
      ),
      PlutoColumn(
        title: "TL *",
        field: "supervisor",
        type: PlutoColumnType.select(
          enableColumnFilter: true,
          ctrl.users.map((u) => u.name).toList(),
        ),
        renderer: (rendererContext) {
          final val = rendererContext.cell.value as String? ?? '';
          final task =
              rendererContext.row.cells['actions']!.value as TaskModel?;
          final isNewRow = task != null && (task.docId.isEmpty);
          final isEmptyRequired = isNewRow && val.isEmpty;
          return Container(
            color: isEmptyRequired ? Colors.red.withOpacity(0.2) : null,
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.symmetric(horizontal: 6),
            child: Text(val),
          );
        },
        // width: 120,
        enableEditingMode: true,
      ),

      PlutoColumn(
        title: "Hours *",
        field: "hours",
        type: PlutoColumnType.text(),
        // width: 90,
        enableEditingMode: false,
        renderer: (rendererContext) {
          final cell = rendererContext.cell;
          final currentText = cell.value as String? ?? '';
          final task =
              rendererContext.row.cells['actions']!.value as TaskModel?;
          final isNewRow = task != null && (task.docId.isEmpty);
          // final isEmptyRequired = isNewRow && (currentText.isEmpty);

          final totalMinutes = _parseHoursString(currentText)?.inMinutes ?? 0;
          final isInvalidHours = isNewRow && (totalMinutes == 0);

          Future<void> openPicker() async {
            final initialDuration =
                _parseHoursString(currentText) ??
                const Duration(hours: 0, minutes: 0);
            final picked = await showDurationPicker(
              context: context,
              initialTime: initialDuration,
            );
            if (picked != null) {
              final formatted = _formatDuration(picked);
              // Update UI cell
              cell.value = formatted;
              rendererContext.stateManager.notifyListeners();

              final ctrl = Get.find<HomeCtrl>();
              if (task != null) {
                if (task.docId.isEmpty) {
                  final updated = _updateTaskModelField(
                    task,
                    'hours',
                    formatted,
                    ctrl,
                  );
                  rendererContext.row.cells['actions']!.value = updated;
                  newTaskBeingAdded = updated;
                  if (_isNewTaskValid(updated)) {
                    try {
                      final data = updated.toSnap();
                      final docRef = await FBFireStore.tasks.add(data);
                      final created = updated.copyWith(docId: docRef.id);
                      rendererContext.row.cells['actions']!.value = created;
                      final idx = tasks.indexWhere((t) => identical(t, task));
                      if (idx != -1) tasks[idx] = created;
                      newTaskBeingAdded = null;
                      await _loadTasks();

                      // Send email notification for new task
                      final emailSent = await sendTaskAssignmentEmail(
                        task: created,
                      );

                      if (emailSent) {
                        showSnackBar(
                          'New task added and email notifications sent',
                        );
                      } else {
                        showSnackBar(
                          'New task added but email notifications failed',
                        );
                      }
                    } catch (e) {
                      showSnackBar('Failed to add new task: $e');
                    }
                  }
                } else {
                  final updateData = {'actualHours': formatted};
                  final updatedTask = task.copyWith(actualHours: formatted);
                  rendererContext.row.cells['actions']!.value = updatedTask;
                  if (_debounce?.isActive ?? false) _debounce!.cancel();
                  _debounce = Timer(const Duration(milliseconds: 200), () {
                    _updateTaskInFirebase(updatedTask, updateData);
                  });
                }
              }
            }
          }

          return InkWell(
            onTap: openPicker,
            child: Container(
              color: isInvalidHours ? Colors.red.withOpacity(0.2) : null,
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: Text(currentText),
            ),
          );
        },
      ),

      PlutoColumn(
        title: "Status *",
        field: "status",
        type: PlutoColumnType.select(TaskStatus.allStatus),
        // width: 120,
        enableEditingMode: true,
      ),

      PlutoColumn(
        title: "Result",
        field: "result",
        type: PlutoColumnType.text(),
        // width: 120,
        enableEditingMode: true,
      ),

      PlutoColumn(
        title: "Report Sent",
        field: "reportSentStatus",
        type: PlutoColumnType.select(reportSentStatus),
        // width: 130,
        enableEditingMode: true,
      ),
      PlutoColumn(
        title: "Customer Call",
        field: "customerCallStatus",
        type: PlutoColumnType.select(customerCallStatus),
        // width: 150,
        enableEditingMode: true,
      ),
      PlutoColumn(
        title: "Call Summary",
        field: "callSummary",
        type: PlutoColumnType.select(callSummary),
        // width: 140,
        enableEditingMode: true,
      ),
      PlutoColumn(
        title: "QC File",
        field: "qcFile",
        type: PlutoColumnType.text(),
        // width: 120,
        enableEditingMode: false,
        renderer: (rendererContext) {
          final task = rendererContext.row.cells['actions']!.value as TaskModel;
          final qcId = task.qcFile;
          final qcTitle = getQcTitle(ctrl.masterQcs, qcId) ?? 'N/A';

          // print('Renderer called for taskId: ${task.docId}');
          // print('QC Id: $qcId');
          // print('QC Title: $qcTitle');
          // print('RendererContext cell value: ${rendererContext.cell.value}');

          return InkWell(
            onTap: () async {
              // print('QC File tapped for taskId: ${task.docId}');
              final qc = ctrl.masterQcs.firstWhereOrNull(
                (m) => m.docId == qcId,
              );
              // final uid = ctrl.loggedInUser?.docId ?? '';
              // final userAnswersMap = task.answers != null && uid.isNotEmpty
              //     ? Map<String, dynamic>.from(task.answers![uid] ?? {})
              //     : null;

              if (qc != null) {
                final result = await showDialog<bool>(
                  context: context,
                  builder: (context) => QcFormDialog(
                    ctrl: ctrl,
                    masterQc: qc,
                    taskDocId: task.docId,
                    userUid: ctrl.loggedInUser?.docId ?? '',
                    // existingAnswers: userAnswersMap,
                    // taskStatus: task.status ?? '',
                  ),
                );
                if (result == true) {
                  // QC form was submitted
                  await _loadTasks();
                  // Additionally update GridStateManager if needed
                  if (gridStateManager != null) {
                    // Find the updated row and refresh
                    final updatedTaskIndex = tasks.indexWhere(
                      (t) => t.docId == task.docId,
                    );
                    if (updatedTaskIndex != -1) {
                      final ctrl = Get.find<HomeCtrl>();
                      final updatedRow = _buildRowsFromTasks([
                        tasks[updatedTaskIndex],
                      ], ctrl).first;
                      final existingRow = gridStateManager!.rows
                          .firstWhereOrNull(
                            (row) =>
                                (row.cells['actions']!.value as TaskModel)
                                    .docId ==
                                task.docId,
                          );

                      if (existingRow != null) {
                        // Update cells of the existing row based on updatedRow
                        updatedRow.cells.forEach((key, value) {
                          if (existingRow.cells.containsKey(key)) {
                            existingRow.cells[key]?.value = value.value;
                          }
                        });

                        gridStateManager!.notifyListeners();
                      }

                      gridStateManager!.notifyListeners();
                    }
                  }
                }
              } else {
                showCtcAppSnackBar(context, 'QC data not found');
              }
            },

            child: Text(qcTitle),
          );
        },
      ),
      PlutoColumn(
        title: "Errors",
        field: "numOfErrors",
        type: PlutoColumnType.number(),
        // width: 100,
        enableEditingMode: true,
        renderer: (rendererContext) {
          final val = rendererContext.cell.value;
          final numberVal = val is int
              ? val
              : int.tryParse(val.toString()) ?? 0;
          final color = numberVal > 0 ? Colors.red : Colors.black;
          return Text(
            val.toString(),
            style: TextStyle(
              color: color,
              fontWeight: numberVal > 0 ? FontWeight.bold : FontWeight.normal,
            ),
          );
        },
      ),
      PlutoColumn(
        title: "Links and Comments",
        field: "linksandcomments",
        type: PlutoColumnType.text(),
        // width: 100,
        enableEditingMode: true,
      ),
      PlutoColumn(
        title: "Actions",
        field: "actions",
        type: PlutoColumnType.text(),
        // width: 120,
        readOnly: true,
        renderer: (rendererContext) {
          final task = rendererContext.cell.value as TaskModel;
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (ctrl.userRoles.contains(Permissions.canDeleteTask))
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  tooltip: 'Delete',
                  splashRadius: 20,
                  padding: EdgeInsets.zero,
                  onPressed: () => _confirmDeleteTask(task),
                ),
            ],
          );
        },
      ),
    ];
  }
}

class _CustomerEditingCell extends StatefulWidget {
  final String initialValue;
  final List<Customermodel> customers;
  final Function(String) onChanged;
  final VoidCallback onEditingComplete;

  const _CustomerEditingCell({
    required this.initialValue,
    required this.customers,
    required this.onChanged,
    required this.onEditingComplete,
  });

  @override
  State<_CustomerEditingCell> createState() => _CustomerEditingCellState();
}

class _CustomerEditingCellState extends State<_CustomerEditingCell> {
  late TextEditingController _controller;
  List<Customermodel> _filteredCustomers = [];
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _removeOverlay();
    _controller.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final query = _controller.text.toLowerCase().trim();

    if (query.isEmpty) {
      _filteredCustomers = [];
      _removeOverlay();
    } else {
      _filteredCustomers = widget.customers.where((customer) {
        return customer.name.toLowerCase().contains(query) ||
            customer.companyName.toLowerCase().contains(query) ||
            (customer.email?.toLowerCase().contains(query) ?? false);
      }).toList();

      if (_filteredCustomers.isNotEmpty) {
        _showOverlay();
      } else {
        _removeOverlay();
      }
    }

    widget.onChanged(_controller.text);
  }

  void _showOverlay() {
    _removeOverlay();

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: 300,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0, 35),
          child: Material(
            elevation: 4,
            child: Container(
              constraints: BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(4),
              ),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _filteredCustomers.length,
                itemBuilder: (context, index) {
                  final customer = _filteredCustomers[index];
                  return ListTile(
                    dense: true,
                    title: Text(customer.name, style: TextStyle(fontSize: 14)),
                    subtitle: Text(
                      customer.companyName,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    trailing: customer.email?.isNotEmpty == true
                        ? Text(
                            customer.email!,
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey.shade500,
                            ),
                          )
                        : null,
                    onTap: () {
                      _controller.text = customer.name;
                      widget.onChanged(customer.name);
                      _removeOverlay();
                      widget.onEditingComplete();
                    },
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );

    if (mounted) {
      Overlay.of(context).insert(_overlayEntry!);
    }
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextField(
        controller: _controller,
        autofocus: true,
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 6, vertical: 8),
          hintText: 'Type to search customers...',
        ),
        onSubmitted: (value) {
          _removeOverlay();
          widget.onEditingComplete();
        },
        onTapOutside: (event) {
          _removeOverlay();
          widget.onEditingComplete();
        },
      ),
    );
  }
}
