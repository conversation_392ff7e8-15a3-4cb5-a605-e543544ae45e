import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class Employee {
  int id;
  String name;
  String designation;
  int salary;

  Employee({
    required this.id,
    required this.name,
    required this.designation,
    required this.salary,
  });
}

List<Employee> getEmployeeData() {
  return [
    Employee(id: 1, name: '<PERSON>', designation: 'Developer', salary: 50000),
    Employee(id: 2, name: '<PERSON>', designation: 'Manager', salary: 70000),
    Employee(id: 3, name: '<PERSON>', designation: 'QA Testing', salary: 40000),
    Employee(id: 4, name: '<PERSON>', designation: 'Support', salary: 35000),
    Employee(id: 5, name: '<PERSON>', designation: 'UI Designer', salary: 45000),
  ];
}

class TrialPage extends StatefulWidget {
  const TrialPage({super.key});

  @override
  State<TrialPage> createState() => _TrialPageState();
}

class _TrialPageState extends State<TrialPage> {
  late List<Employee> _employees;
  late EmployeeDataSource _employeeDataSource;

  @override
  void initState() {
    super.initState();
    _employees = getEmployeeData();
    _employeeDataSource = EmployeeDataSource(_employees);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Syncfusion Flutter DataGrid')),
      body: SfDataGrid(
        source: _employeeDataSource,
        columnWidthMode: ColumnWidthMode.auto,
        columns: <GridColumn>[
          GridColumn(
            columnName: 'id',
            label: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              alignment: Alignment.center,
              child: const Text('ID'),
            ),
          ),
          GridColumn(
            columnName: 'name',
            label: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              alignment: Alignment.center,
              child: const Text('Name'),
            ),
          ),
          GridColumn(
            columnName: 'designation',
            label: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              alignment: Alignment.center,
              child: const Text('Designation'),
            ),
          ),
          GridColumn(
            columnName: 'salary',
            label: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              alignment: Alignment.center,
              child: const Text('Salary'),
            ),
          ),
        ],
      ),
    );
  }
}

class EmployeeDataSource extends DataGridSource {
  EmployeeDataSource(this.employees) {
    dataGridRows = employees
        .map<DataGridRow>(
          (e) => DataGridRow(
            cells: <DataGridCell>[
              DataGridCell<int>(columnName: 'id', value: e.id),
              DataGridCell<String>(columnName: 'name', value: e.name),
              DataGridCell<String>(
                columnName: 'designation',
                value: e.designation,
              ),
              DataGridCell<int>(columnName: 'salary', value: e.salary),
            ],
          ),
        )
        .toList();
  }

  final List<Employee> employees;

  late List<DataGridRow> dataGridRows;

  final TextStyle textStyle = const TextStyle(
    fontFamily: 'Roboto',
    fontWeight: FontWeight.w400,
    fontSize: 14,
    color: Colors.black87,
  );

  final List<String> dropDownMenuItems = [
    'Manager',
    'Project Lead',
    'Developer',
    'Support',
    'QA Testing',
    'UI Designer',
    'Sales Representative',
    'Sales Associate',
    'Administrator',
  ];

  @override
  List<DataGridRow> get rows => dataGridRows;

  @override
  DataGridRowAdapter? buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((dataGridCell) {
        if (dataGridCell.columnName == 'designation') {
          return Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: SearchableDropdownCell(
              initialValue: dataGridCell.value ?? '',
              options: dropDownMenuItems,
              onValueChanged: (newValue) {
                final int rowIndex = dataGridRows.indexOf(row);
                // Update DataGridRow
                dataGridRows[rowIndex].getCells()[2] = DataGridCell<String>(
                  columnName: 'designation',
                  value: newValue,
                );
                // Update underlying data source
                employees[rowIndex].designation = newValue;
                notifyListeners();
              },
            ),
          );
        }

        // Other cells just display text
        return Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Text(
            dataGridCell.value.toString(),
            overflow: TextOverflow.ellipsis,
            style: textStyle,
          ),
        );
      }).toList(),
    );
  }
}

class SearchableDropdownCell extends StatefulWidget {
  final String initialValue;
  final List<String> options;
  final Function(String) onValueChanged;

  const SearchableDropdownCell({
    super.key,
    required this.initialValue,
    required this.options,
    required this.onValueChanged,
  });

  @override
  _SearchableDropdownCellState createState() => _SearchableDropdownCellState();
}

class _SearchableDropdownCellState extends State<SearchableDropdownCell> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  List<String> _filteredOptions = [];
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _focusNode = FocusNode();
    _filteredOptions = widget.options;
    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  void _onTextChanged() {
    final query = _controller.text.toLowerCase();
    setState(() {
      _filteredOptions = widget.options
          .where((option) => option.toLowerCase().contains(query))
          .toList();
    });
    if (query.isNotEmpty && _filteredOptions.isNotEmpty) {
      _showOverlay();
    } else {
      _removeOverlay();
    }
    widget.onValueChanged(_controller.text);
  }

  void _onFocusChanged() {
    final query = _controller.text.toLowerCase();
    if (!_focusNode.hasFocus) {
      _removeOverlay();
    } else if (query.isNotEmpty && _filteredOptions.isNotEmpty) {
      _showOverlay();
    }
  }

  void _showOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
      return;
    }
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: 200,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: const Offset(0, 40),
          child: Material(
            elevation: 4,
            child: ListView(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              children: _filteredOptions.map((option) {
                return ListTile(
                  title: Text(option),
                  onTap: () {
                    _controller.text = option;
                    widget.onValueChanged(option);
                    _removeOverlay();
                    _focusNode.unfocus();
                  },
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  void dispose() {
    _removeOverlay();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 8,
            vertical: 6,
          ),
          hintText: 'Select',
        ),
      ),
    );
  }
}
