import 'dart:async';
import 'dart:io';
import 'package:excel/excel.dart' as excel;
import 'package:file_saver/file_saver.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:duration_picker/duration_picker.dart';
import 'package:legacy_pms/common/common_searchbar.dart';
import 'package:legacy_pms/common/custom_header_button.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/models/customermodel.dart';
import 'package:legacy_pms/models/masterqcmodel.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:legacy_pms/views/worksheet/qcform_dialog.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:legacy_pms/models/taskmodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/firebase.dart';

class SearchableDropdownCell extends StatefulWidget {
  final String initialValue;
  final List<String> options;
  final Function(String) onValueChanged;
  final VoidCallback? onEditingComplete;

  const SearchableDropdownCell({
    super.key,
    required this.initialValue,
    required this.options,
    required this.onValueChanged,
    this.onEditingComplete,
  });

  @override
  SearchableDropdownCellState createState() => SearchableDropdownCellState();
}

class SearchableDropdownCellState extends State<SearchableDropdownCell> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  List<String> _filteredOptions = [];
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _focusNode = FocusNode();
    _filteredOptions = widget.options;
    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  void _onTextChanged() {
    final query = _controller.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredOptions = widget.options;
      } else {
        _filteredOptions = widget.options
            .where((option) => option.toLowerCase().contains(query))
            .toList();
      }
    });

    // Show overlay when typing and there are options to show
    if (_focusNode.hasFocus &&
        _filteredOptions.isNotEmpty &&
        query.isNotEmpty) {
      _showOverlay();
    } else if (query.isEmpty) {
      _removeOverlay();
    }
    // Don't call onValueChanged on every text change - only when user selects or completes editing
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      _removeOverlay();
      // Call onValueChanged when focus is lost to update the cell value
      widget.onValueChanged(_controller.text);
      widget.onEditingComplete?.call();
    } else if (_filteredOptions.isNotEmpty) {
      // Show overlay when focused if there are options to show
      _showOverlay();
    }
  }

  void _showOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
      return;
    }
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: 250,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: const Offset(0, 35),
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(4),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(4),
              ),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: _filteredOptions.length,
                itemBuilder: (context, index) {
                  final option = _filteredOptions[index];
                  return InkWell(
                    onTap: () {
                      _controller.text = option;
                      widget.onValueChanged(option);
                      _removeOverlay();
                      _focusNode.unfocus();
                      widget.onEditingComplete?.call();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      child: Text(option, style: const TextStyle(fontSize: 14)),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
    if (mounted) {
      Overlay.of(context).insert(_overlayEntry!);
    }
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  void dispose() {
    _removeOverlay();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        autofocus: true,
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 8,
            vertical: 6,
          ),
          hintText: 'Type to search...',
        ),
        onSubmitted: (value) {
          _removeOverlay();
          widget.onEditingComplete?.call();
        },
        onTapOutside: (event) {
          _removeOverlay();
          _focusNode.unfocus();
        },
      ),
    );
  }
}

class DataGridTasksPage extends StatefulWidget {
  const DataGridTasksPage({super.key});

  @override
  State<DataGridTasksPage> createState() => _DataGridTasksPageState();
}

class _DataGridTasksPageState extends State<DataGridTasksPage> {
  List<TaskModel> tasks = [];

  bool isLoading = true;

  bool isQcSubmitted = true;

  late TaskDataSource taskDataSource;
  DataGridController dataGridController = DataGridController();

  bool isExporting = false;
  Timer? _debounce;

  SearchController searchctrl = SearchController();
  TaskModel? newTaskBeingAdded;

  int parseHoursToMinutes(String? hoursStr) {
    if (hoursStr == null || hoursStr.trim().isEmpty) return 0;

    try {
      final parts = hoursStr.contains(':')
          ? hoursStr.split(':')
          : [hoursStr.substring(0, 2), hoursStr.substring(2)];

      final hours = int.parse(parts[0]);
      final minutes = int.parse(parts[1]);
      return hours * 60 + minutes;
    } catch (e) {
      return 0; // Invalid format
    }
  }

  bool _isNewTaskValid(TaskModel task) {
    final totalMinutes = parseHoursToMinutes(task.actualHours);
    return task.customer.isNotEmpty &&
        task.employee.isNotEmpty &&
        task.supervisor.isNotEmpty &&
        task.details.isNotEmpty &&
        totalMinutes > 0 &&
        task.pType != null &&
        task.status != null &&
        task.status!.isNotEmpty;
  }

  void _handleCellTap(DataGridCellTapDetails details, HomeCtrl ctrl) {
    final rowIndex =
        details.rowColumnIndex.rowIndex - 1; // Subtract 1 for header
    if (rowIndex < 0 || rowIndex >= taskDataSource.tasks.length) return;

    final task = taskDataSource.tasks[rowIndex];
    final columnName = details.column.columnName;

    // Only handle non-editable actions here
    switch (columnName) {
      case 'qcFile':
        _showQcFileDialog(task, ctrl);
        break;
      case 'actions':
        if (task.docId.isNotEmpty) {
          _confirmDeleteTask(task);
        }
        break;
      // All other cells are handled by buildEditWidget for inline editing
    }
  }

  // Removed _handleCellActivated method - not needed

  void _updateTaskField(
    TaskModel task,
    String field,
    dynamic value,
    HomeCtrl ctrl,
  ) {
    print(
      '_updateTaskField called: field=$field, value=$value, isNewTask=${task == newTaskBeingAdded}',
    );
    if (task == newTaskBeingAdded) {
      // Handle new task being added
      final updated = _updateTaskModelField(
        newTaskBeingAdded!,
        field,
        value,
        ctrl,
      );
      newTaskBeingAdded = updated;

      // Update the task in the data source
      final taskIndex = taskDataSource.tasks.indexWhere(
        (t) => identical(t, task),
      );
      if (taskIndex != -1) {
        taskDataSource.tasks[taskIndex] = updated;
        taskDataSource.updateTasks(taskDataSource.tasks);
      }

      // If all required fields are valid, save new task
      if (updated.docId.isEmpty && _isNewTaskValid(updated)) {
        _saveNewTask(updated, ctrl);
      }
    } else {
      // Handle existing task update
      final updatedTask = _updateTaskModelField(task, field, value, ctrl);

      // Update the task in the data source
      final taskIndex = taskDataSource.tasks.indexWhere(
        (t) => t.docId == task.docId,
      );
      if (taskIndex != -1) {
        taskDataSource.tasks[taskIndex] = updatedTask;
        taskDataSource.updateTasks(taskDataSource.tasks);
      }

      // Update in Firebase with debouncing
      final updateData = _buildUpdateDataForField(field, value, ctrl);
      if (updateData.isNotEmpty) {
        if (_debounce?.isActive ?? false) _debounce!.cancel();
        _debounce = Timer(const Duration(milliseconds: 700), () {
          _updateTaskInFirebase(updatedTask, updateData);
        });
      }
    }
  }

  Future<void> _saveNewTask(TaskModel task, HomeCtrl ctrl) async {
    try {
      final data = task.toSnap();
      final docRef = await FBFireStore.tasks.add(data);
      final created = task.copyWith(docId: docRef.id);

      // Update the task in the data source
      final taskIndex = taskDataSource.tasks.indexWhere(
        (t) => identical(t, task),
      );
      if (taskIndex != -1) {
        taskDataSource.tasks[taskIndex] = created;
        taskDataSource.updateTasks(taskDataSource.tasks);
      }

      // Clear new task flag
      setState(() {
        newTaskBeingAdded = null;
      });

      // Refresh data
      await _loadTasks();

      // Send email notification
      final emailSent = await sendTaskAssignmentEmail(task: created);
      if (emailSent) {
        showSnackBar('New task added and email notifications sent');
      } else {
        showSnackBar('New task added but email notifications failed');
      }
    } catch (e) {
      showSnackBar('Failed to add new task: $e');
    }
  }

  void _showQcFileDialog(TaskModel task, HomeCtrl ctrl) async {
    final qc = ctrl.masterQcs.where((q) => q.docId == task.qcFile).isNotEmpty
        ? ctrl.masterQcs.where((q) => q.docId == task.qcFile).first
        : null;

    if (qc != null) {
      final result = await showDialog<bool>(
        context: context,
        builder: (context) => QcFormDialog(
          ctrl: ctrl,
          masterQc: qc,
          taskDocId: task.docId,
          userUid: ctrl.loggedInUser?.docId ?? '',
        ),
      );
      if (result == true) {
        // QC form was submitted
        await _loadTasks();
      }
    } else {
      showSnackBar('No QC form associated with this task');
    }
  }

  Future<void> _updateTaskInFirebase(
    TaskModel task,
    Map<String, dynamic> updateData,
  ) async {
    try {
      await FBFireStore.tasks.doc(task.docId).update(updateData);
      showSnackBar("Task updated successfully");
      await _loadTasks();
    } catch (e) {
      showSnackBar("Failed to update task: $e");
      _loadTasks();
    }
  }

  @override
  void initState() {
    super.initState();
    taskDataSource = TaskDataSource(
      tasks: [],
      ctrl: Get.find<HomeCtrl>(),
      updateTaskField: _updateTaskField,
    );
    _loadTasks();
  }

  TaskModel _updateTaskModelField(
    TaskModel task,
    String field,
    dynamic value,
    HomeCtrl ctrl,
  ) {
    TaskModel updated = task;
    switch (field) {
      case 'customer':
        final customer = ctrl.customers.where((c) => c.name == value).isNotEmpty
            ? ctrl.customers.where((c) => c.name == value).first
            : null;
        if (customer != null) {
          updated = updated.copyWith(customer: customer.docId);
        }
        break;
      case 'pType':
        final activity =
            ctrl.masterTasks.where((a) => a.title == value).isNotEmpty
            ? ctrl.masterTasks.where((a) => a.title == value).first
            : null;
        if (activity != null) {
          final String qcId = activity.masterQc;
          updated = updated.copyWith(
            pType: activity.docId,
            qcFile: (qcId.isNotEmpty) ? qcId : updated.qcFile,
          );
        }
        break;
      case 'employee':
        final employee = ctrl.users.where((u) => u.name == value).isNotEmpty
            ? ctrl.users.where((u) => u.name == value).first
            : null;
        if (employee != null) {
          updated = updated.copyWith(employee: employee.docId);
        }
        break;
      case 'supervisor':
        final supervisor = ctrl.users.where((u) => u.name == value).isNotEmpty
            ? ctrl.users.where((u) => u.name == value).first
            : null;
        if (supervisor != null) {
          updated = updated.copyWith(supervisor: supervisor.docId);
        }
        break;
      case 'details':
        updated = updated.copyWith(details: value);
        break;
      case 'status':
        updated = updated.copyWith(status: value);
        break;
      case 'result':
        updated = updated.copyWith(result: value);
        break;
      case 'actualHours':
        updated = updated.copyWith(actualHours: value);
        break;
      case 'hours':
        updated = updated.copyWith(actualHours: value);
        break;
      case 'reportSentStatus':
        updated = updated.copyWith(reportSentStatus: value);
        break;
      case 'customerCallStatus':
        updated = updated.copyWith(customerCallStatus: value);
        break;
      case 'callSummary':
        updated = updated.copyWith(callSummary: value);
        break;
      case 'qcFile':
        updated = updated.copyWith(qcFile: value);
        break;
      case 'numOfErrors':
        updated = updated.copyWith(numOfErrors: value.toString());
        break;
      case 'linksandcomments':
        updated = updated.copyWith(linksAndComments: value);
        break;
    }
    return updated;
  }

  Map<String, dynamic> _buildUpdateDataForField(
    String field,
    dynamic value,
    HomeCtrl ctrl,
  ) {
    Map<String, dynamic> updateData = {};

    switch (field) {
      case 'customer':
        final customer = ctrl.customers.where((c) => c.name == value).isNotEmpty
            ? ctrl.customers.where((c) => c.name == value).first
            : null;
        if (customer != null) updateData['customer'] = customer.docId;
        break;
      case 'pType':
        final activity =
            ctrl.masterTasks.where((a) => a.title == value).isNotEmpty
            ? ctrl.masterTasks.where((a) => a.title == value).first
            : null;
        if (activity != null) {
          updateData['pType'] = activity.docId;
          if (activity.masterQc.isNotEmpty) {
            updateData['qcFile'] = activity.masterQc;
          }
        }
        break;
      case 'employee':
        final employee = ctrl.users.where((u) => u.name == value).isNotEmpty
            ? ctrl.users.where((u) => u.name == value).first
            : null;
        if (employee != null) updateData['employee'] = employee.docId;
        break;
      case 'supervisor':
        final supervisor = ctrl.users.where((u) => u.name == value).isNotEmpty
            ? ctrl.users.where((u) => u.name == value).first
            : null;
        if (supervisor != null) updateData['supervisor'] = supervisor.docId;
        break;
      case 'details':
        updateData['details'] = value;
        break;
      case 'status':
        updateData['status'] = value;
        if ((value as String) == 'Completed') {
          updateData['completed'] = true;
          updateData['completedAt'] = DateTime.now();
        } else {
          updateData['completed'] = false;
          updateData['completedAt'] = null;
        }
        break;
      case 'result':
        updateData['result'] = value;
        break;
      case 'actualHours':
        updateData['actualHours'] = value;
        break;
      case 'hours':
        updateData['actualHours'] = value;
        break;
      case 'reportSentStatus':
        updateData['reportSentStatus'] = value;
        break;
      case 'customerCallStatus':
        updateData['customerCallStatus'] = value;
        break;
      case 'callSummary':
        updateData['callSummary'] = value;
        break;
      case 'qcFile':
        updateData['qcFile'] = value;
        break;
      case 'numOfErrors':
        updateData['numOfErrors'] = value.toString();
        break;
      case 'linksandcomments':
        updateData['linksAndComments'] = value;
        break;
    }

    return updateData;
  }

  Future<void> _loadTasks() async {
    setState(() => isLoading = true);

    try {
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = startOfDay
          .add(Duration(days: 1))
          .subtract(Duration(milliseconds: 1));

      final snapshot = await FBFireStore.tasks
          .where('createdAt', isGreaterThanOrEqualTo: startOfDay)
          .where('createdAt', isLessThanOrEqualTo: endOfDay)
          .get();

      final loadedTasks = snapshot.docs
          .map((doc) => TaskModel.fromSnap(doc))
          .toList();

      loadedTasks.sort(
        (a, b) => a.createdAt.compareTo(b.createdAt),
      ); // FIFO sorting here

      setState(() {
        tasks = loadedTasks;
        isLoading = false;
      });

      // Also refresh PlutoGrid rows if it is loaded
      if (mounted) {
        final ctrl = Get.find<HomeCtrl>();

        // Filter tasks based on permissions
        List<TaskModel> userFilteredTasks;
        if (ctrl.userRoles.contains(Permissions.canSeeAllTasks)) {
          userFilteredTasks = tasks;
        } else {
          final currentUserUid = ctrl.loggedInUser?.docId;
          userFilteredTasks = tasks
              .where(
                (task) =>
                    task.employee == currentUserUid ||
                    task.supervisor == currentUserUid,
              )
              .toList();
        }

        // Apply search filter (use current searchctrl text)
        final query = searchctrl.text.toLowerCase();
        final filteredTasks = query.isEmpty
            ? userFilteredTasks
            : userFilteredTasks.where((task) {
                final activity =
                    ctrl.masterTasks
                        .where((a) => a.docId == task.pType)
                        .isNotEmpty
                    ? ctrl.masterTasks
                          .where((a) => a.docId == task.pType)
                          .first
                          .title
                          .toLowerCase()
                    : '';
                final details = (task.details).toLowerCase();
                final employee =
                    ctrl.users.where((u) => u.docId == task.employee).isNotEmpty
                    ? ctrl.users
                          .where((u) => u.docId == task.employee)
                          .first
                          .name
                          .toLowerCase()
                    : '';
                final supervisor =
                    ctrl.users
                        .where((u) => u.docId == task.supervisor)
                        .isNotEmpty
                    ? ctrl.users
                          .where((u) => u.docId == task.supervisor)
                          .first
                          .name
                          .toLowerCase()
                    : '';
                return activity.contains(query) ||
                    details.contains(query) ||
                    employee.contains(query) ||
                    supervisor.contains(query);
              }).toList();

        taskDataSource.updateTasks(filteredTasks);
      }
    } catch (e) {
      setState(() => isLoading = false);
      showSnackBar('Failed to load tasks: $e');
    }
  }

  // void _showEditTaskDialog(TaskModel task) {
  //   showDialog(
  //     context: context,
  //     barrierDismissible: false,
  //     builder: (context) => AddTaskDialog(task: task),
  //   ).then((_) => _loadTasks());
  // }

  Future<void> _confirmDeleteTask(TaskModel task) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: popupBgColor,
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this task?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Yes'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await FBFireStore.tasks.doc(task.docId).delete();
        showSnackBar('Task deleted successfully');
        _loadTasks();
      } catch (e) {
        showSnackBar('Failed to delete task: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final query = searchctrl.text.toLowerCase();
        final currentUser = ctrl.loggedInUser;
        final currentUserUid = currentUser?.docId;

        // Filter tasks based on permissions
        List<TaskModel> userFilteredTasks;
        if (ctrl.userRoles.contains(Permissions.canSeeAllTasks)) {
          // User can see all tasks
          userFilteredTasks = tasks;
        } else {
          // User can only see their own tasks
          userFilteredTasks = tasks
              .where(
                (task) =>
                    // task.customer == currentUserUid ||
                    task.employee == currentUserUid ||
                    task.supervisor == currentUserUid,
              )
              .toList();
        }

        // Apply search filter on the user-filtered tasks
        final filteredTasks = query.isEmpty
            ? userFilteredTasks
            : userFilteredTasks.where((task) {
                // final customer =
                //     ctrl.customers
                //         .firstWhereOrNull((c) => c.docId == task.customer)
                //         ?.name
                //         .toLowerCase() ??
                //     '';
                final activity =
                    ctrl.masterTasks
                        .where((a) => a.docId == task.pType)
                        .isNotEmpty
                    ? ctrl.masterTasks
                          .where((a) => a.docId == task.pType)
                          .first
                          .title
                          .toLowerCase()
                    : '';
                final details = (task.details).toLowerCase();
                final employee =
                    ctrl.users.where((u) => u.docId == task.employee).isNotEmpty
                    ? ctrl.users
                          .where((u) => u.docId == task.employee)
                          .first
                          .name
                          .toLowerCase()
                    : '';
                final supervisor =
                    ctrl.users
                        .where((u) => u.docId == task.supervisor)
                        .isNotEmpty
                    ? ctrl.users
                          .where((u) => u.docId == task.supervisor)
                          .first
                          .name
                          .toLowerCase()
                    : '';

                return
                // customer.contains(query) ||
                activity.contains(query) ||
                    details.contains(query) ||
                    employee.contains(query) ||
                    supervisor.contains(query);
              }).toList();

        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// HEADER BAR
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        "Tasks (${filteredTasks.length})",
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: Colors.black,
                          letterSpacing: 0.8,
                        ),
                      ),
                      SizedBox(width: 20),
                      CommonSearchBar(
                        searchController: searchctrl,
                        searchOnChanged: (value) {
                          setState(() {
                            taskDataSource.updateTasks(filteredTasks);
                          });
                        },
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      isExporting
                          ? CircularProgressIndicator()
                          : CustomHeaderButton(
                              onPressed:
                                  // ctrl.userRoles.contains(Permissions.canAddTask)
                                  // ? _showAddTaskDialog
                                  // :
                                  () async {
                                    await exportTasksToExcel(
                                      tasks: filteredTasks,
                                      ctrl: ctrl,
                                      context: context,
                                    );
                                  },
                              buttonName: "Export",
                            ),
                      // const SizedBox(width: 10),
                      // Test reminder emails button (for testing purposes)
                      /*   if (ctrl.userRoles.contains(Permissions.canAddTask))
                        CustomHeaderButton(
                          onPressed: () async {
                            showSnackBar('Testing reminder emails...');

                            // Test all reminder types with detailed results
                            final result12pm =
                                await sendTaskReminderEmailsDetailed(
                                  reminderType: '12pm',
                                );
                            final result3pm =
                                await sendTaskReminderEmailsDetailed(
                                  reminderType: '3pm',
                                );
                            final result430pm =
                                await sendTaskReminderEmailsDetailed(
                                  reminderType: '4:30pm',
                                );

                            // Calculate total emails sent
                            final totalEmails =
                                (result12pm['emailsSent'] ?? 0) +
                                (result3pm['emailsSent'] ?? 0) +
                                (result430pm['emailsSent'] ?? 0);

                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: Text('Reminder Test Results'),
                                content: SizedBox(
                                  width: 500,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Test completed for all reminder types:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      SizedBox(height: 15),

                                      // 12pm results
                                      Text(
                                        '12pm Reminder:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      Text(
                                        '  Status: ${result12pm['success'] ? 'Success' : 'Failed'}',
                                      ),
                                      Text(
                                        '  Emails sent: ${result12pm['emailsSent'] ?? 0}',
                                      ),
                                      if (result12pm['message'] != null)
                                        Text(
                                          '  Message: ${result12pm['message']}',
                                        ),
                                      SizedBox(height: 8),

                                      // 3pm results
                                      Text(
                                        '3pm Reminder:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      Text(
                                        '  Status: ${result3pm['success'] ? 'Success' : 'Failed'}',
                                      ),
                                      Text(
                                        '  Emails sent: ${result3pm['emailsSent'] ?? 0}',
                                      ),
                                      if (result3pm['message'] != null)
                                        Text(
                                          '  Message: ${result3pm['message']}',
                                        ),
                                      SizedBox(height: 8),

                                      // 4:30pm results
                                      Text(
                                        '4:30pm Reminder:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      Text(
                                        '  Status: ${result430pm['success'] ? 'Success' : 'Failed'}',
                                      ),
                                      Text(
                                        '  Emails sent: ${result430pm['emailsSent'] ?? 0}',
                                      ),
                                      if (result430pm['message'] != null)
                                        Text(
                                          '  Message: ${result430pm['message']}',
                                        ),
                                      SizedBox(height: 15),

                                      // Summary
                                      Container(
                                        padding: EdgeInsets.all(10),
                                        decoration: BoxDecoration(
                                          color: Colors.grey[100],
                                          borderRadius: BorderRadius.circular(
                                            5,
                                          ),
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'Summary:',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            Text(
                                              'Total emails sent: $totalEmails',
                                            ),
                                            if (totalEmails == 0)
                                              Text(
                                                'No emails were sent. This may be because there are no tasks created today that match the reminder criteria.',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  fontStyle: FontStyle.italic,
                                                  color: Colors.orange[700],
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(),
                                    child: Text('OK'),
                                  ),
                                ],
                              ),
                            );
                          },
                          buttonName: "Test Reminders",
                        ), */
                      const SizedBox(width: 10),

                      //Add task button
                      CustomHeaderButton(
                        onPressed:
                            ctrl.userRoles.contains(Permissions.canAddTask)
                            ? () async {
                                // final result = await FBFunctions.ff
                                //     .httpsCallable('testLoggedInUsersCheck')
                                //     .call();
                                // print("result: ${result.data}");
                                setState(() {
                                  newTaskBeingAdded = TaskModel.empty();
                                  tasks.add(newTaskBeingAdded!);
                                  taskDataSource.updateTasks(tasks);
                                });
                              }
                            : () {},
                        buttonName: "Add Task",
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 20),

              /// CONTENT (Grid or Empty state)
              Expanded(
                child: isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : filteredTasks.isEmpty
                    ? _buildEmptyState()
                    : SfDataGrid(
                        source: taskDataSource,
                        controller: dataGridController,
                        columns: _buildDataGridColumns(ctrl),
                        allowEditing: true,
                        allowSorting: true,
                        allowFiltering: true,
                        editingGestureType: EditingGestureType.tap,
                        selectionMode: SelectionMode.single,
                        navigationMode: GridNavigationMode.cell,
                        columnWidthMode: ColumnWidthMode.auto,
                        gridLinesVisibility: GridLinesVisibility.both,
                        headerGridLinesVisibility: GridLinesVisibility.both,
                        rowHeight: 45,
                        headerRowHeight: 50,
                        onCellTap: (details) {
                          print('Cell tapped: ${details.column.columnName}');
                          // Only handle specific non-editable actions
                          final columnName = details.column.columnName;
                          if (columnName == 'qcFile' ||
                              columnName == 'actions') {
                            _handleCellTap(details, ctrl);
                          }
                          // For all other columns, let the DataGrid handle the tap
                        },
                      ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.task_outlined, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            "No tasks found",
            style: TextStyle(
              fontSize: 20,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            "Tap 'Add Task' to create your first one.",
            style: TextStyle(color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  // Removed _updateTaskModelField method - not needed for Syncfusion DataGrid

  // Removed _buildUpdateDataForField method - not needed for Syncfusion DataGrid

  Future<void> exportTasksToExcel({
    required List<TaskModel> tasks,
    required HomeCtrl ctrl,
    required BuildContext context,
  }) async {
    if (isExporting) return; // Prevent multiple exports

    try {
      setState(() {
        isExporting = true; // Start loader in UI
      });

      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile['Sheet1'];

      final headers = [
        excel.TextCellValue('Date'),
        excel.TextCellValue('Customer'),
        excel.TextCellValue('Activity Type'),
        excel.TextCellValue('Tasks'),
        excel.TextCellValue('Employee'),
        excel.TextCellValue('TL'),
        excel.TextCellValue('Status'),
        excel.TextCellValue('Result'),
        excel.TextCellValue('Hours'),
        excel.TextCellValue('Report Sent'),
        excel.TextCellValue('Customer Call'),
        excel.TextCellValue('Call Summary'),
        excel.TextCellValue('QC File'),
        excel.TextCellValue('Errors'),
      ];

      sheet.appendRow(headers);

      for (final task in tasks) {
        final customer =
            ctrl.customers.where((c) => c.docId == task.customer).isNotEmpty
            ? ctrl.customers.where((c) => c.docId == task.customer).first.name
            : '';
        final employee =
            ctrl.users.where((u) => u.docId == task.employee).isNotEmpty
            ? ctrl.users.where((u) => u.docId == task.employee).first.name
            : '';
        final supervisor =
            ctrl.users.where((u) => u.docId == task.supervisor).isNotEmpty
            ? ctrl.users.where((u) => u.docId == task.supervisor).first.name
            : '';
        final activity =
            ctrl.masterTasks.where((a) => a.docId == task.pType).isNotEmpty
            ? ctrl.masterTasks.where((a) => a.docId == task.pType).first.title
            : '';

        final qcFileName = getQcTitle(ctrl.masterQcs, task.qcFile) ?? '';
        final dateString = DateFormat('dd-MM-yyyy').format(task.createdAt);

        final row = [
          excel.TextCellValue(dateString),
          excel.TextCellValue(customer),
          excel.TextCellValue(activity),
          excel.TextCellValue(task.details),
          excel.TextCellValue(employee),
          excel.TextCellValue(supervisor),
          excel.TextCellValue(task.status ?? ''),
          excel.TextCellValue(task.result ?? ''),
          excel.TextCellValue(task.actualHours ?? ''),
          excel.TextCellValue(task.reportSentStatus ?? ''),
          excel.TextCellValue(task.customerCallStatus ?? ''),
          excel.TextCellValue(task.callSummary ?? ''),
          excel.TextCellValue(qcFileName),
          excel.TextCellValue(task.numOfErrors ?? ''),
        ];

        sheet.appendRow(row);
      }

      final excelBytes = excelFile.encode();

      final fileName = 'Tasks_${FBAuth.auth.currentUser?.email}.xlsx';

      if (kIsWeb) {
        await FileSaver.instance.saveFile(
          name: fileName,
          bytes: Uint8List.fromList(excelBytes!),
          fileExtension: 'xlsx',
          mimeType: MimeType.microsoftExcel,
        );
      } else {
        final dir = await getExternalStorageDirectory();
        final filePath = '${dir!.path}/$fileName';
        await File(filePath).writeAsBytes(excelBytes!);
        showSnackBar('Exported to $filePath');
      }
    } catch (e) {
      showSnackBar('Failed to export tasks: $e');
    } finally {
      setState(() {
        isExporting = false; // Stop loader in UI for both success and failure
      });
    }
  }

  // Removed _updateTaskInFirebase method - not needed for Syncfusion DataGrid

  String? getQcTitle(List<Masterqcmodel> qcs, String? id) {
    if (id == null) return null;
    final qc = qcs.where((item) => item.docId == id).isNotEmpty
        ? qcs.where((item) => item.docId == id).first
        : null;
    return qc?.title;
  }

  /// Syncfusion DataGrid Columns
  List<GridColumn> _buildDataGridColumns(HomeCtrl ctrl) {
    return [
      GridColumn(
        columnName: 'createdAt',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Date',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: false,
      ),
      GridColumn(
        columnName: 'customer',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Customer *',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'details',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Tasks *',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'employee',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Employee *',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'supervisor',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Supervisor',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'pType',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Activity Type *',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'hours',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Hours',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'status',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Status',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'benchmark',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Benchmark',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: false,
      ),
      GridColumn(
        columnName: 'result',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Result',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'actualHours',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Actual Hours',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'reportSentStatus',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Report Sent',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'customerCallStatus',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Customer Call',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'callSummary',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Call Summary',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'qcFile',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'QC File',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: false,
      ),
      GridColumn(
        columnName: 'numOfErrors',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Errors',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'linksandcomments',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Links & Comments',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: true,
      ),
      GridColumn(
        columnName: 'actions',
        label: Container(
          padding: const EdgeInsets.all(8.0),
          alignment: Alignment.center,
          child: const Text(
            'Actions',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        allowEditing: false,
      ),
    ];
  }
}

class _CustomerEditingCell extends StatefulWidget {
  final String initialValue;
  final List<Customermodel> customers;
  final Function(String) onChanged;
  final VoidCallback onEditingComplete;
  const _CustomerEditingCell({
    required this.initialValue,
    required this.customers,
    required this.onChanged,
    required this.onEditingComplete,
  });
  @override
  State<_CustomerEditingCell> createState() => _CustomerEditingCellState();
}

class _CustomerEditingCellState extends State<_CustomerEditingCell> {
  late TextEditingController _controller;
  List<Customermodel> _filteredCustomers = [];
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _removeOverlay();
    _controller.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final query = _controller.text.toLowerCase().trim();

    if (query.isEmpty) {
      _filteredCustomers = [];
      _removeOverlay();
    } else {
      _filteredCustomers = widget.customers.where((customer) {
        return customer.name.toLowerCase().contains(query) ||
            customer.companyName.toLowerCase().contains(query) ||
            (customer.email?.toLowerCase().contains(query) ?? false);
      }).toList();

      if (_filteredCustomers.isNotEmpty) {
        _showOverlay();
      } else {
        _removeOverlay();
      }
    }

    widget.onChanged(_controller.text);
  }

  void _showOverlay() {
    _removeOverlay();

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: 300,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0, 35),
          child: Material(
            elevation: 4,
            child: Container(
              constraints: BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(4),
              ),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _filteredCustomers.length,
                itemBuilder: (context, index) {
                  final customer = _filteredCustomers[index];
                  return ListTile(
                    dense: true,
                    title: Text(customer.name, style: TextStyle(fontSize: 14)),
                    subtitle: Text(
                      customer.companyName,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    trailing: customer.email?.isNotEmpty == true
                        ? Text(
                            customer.email!,
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey.shade500,
                            ),
                          )
                        : null,
                    onTap: () {
                      _controller.text = customer.name;
                      widget.onChanged(customer.name);
                      _removeOverlay();
                      widget.onEditingComplete();
                    },
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );

    if (mounted) {
      Overlay.of(context).insert(_overlayEntry!);
    }
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextField(
        controller: _controller,
        autofocus: true,
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 6, vertical: 8),
          hintText: 'Type to search customers...',
        ),
        onSubmitted: (value) {
          _removeOverlay();
          widget.onEditingComplete();
        },
        onTapOutside: (event) {
          _removeOverlay();
          widget.onEditingComplete();
        },
      ),
    );
  }
}

class _SimpleDropdownEditingCell extends StatelessWidget {
  final List<String> items;
  final String value;
  final ValueChanged<String?> onChanged;
  const _SimpleDropdownEditingCell({
    required this.items,
    required this.value,
    required this.onChanged,
  });
  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<String>(
      isDense: true,
      value: items.contains(value) ? value : null,
      items: items
          .map((e) => DropdownMenuItem(value: e, child: Text(e)))
          .toList(),
      onChanged: onChanged,
      autofocus: true,
      decoration: InputDecoration(
        border: InputBorder.none,
        isDense: true,
        contentPadding: EdgeInsets.symmetric(horizontal: 6, vertical: 8),
      ),
    );
  }
}

class _SimpleTextEditingCell extends StatelessWidget {
  final String value;
  final ValueChanged<String> onChanged;
  final TextInputType? keyboardType;
  const _SimpleTextEditingCell({
    required this.value,
    required this.onChanged,
    this.keyboardType,
  });
  @override
  Widget build(BuildContext context) {
    final controller = TextEditingController(text: value);
    return TextField(
      controller: controller,
      autofocus: true,
      keyboardType: keyboardType,
      onSubmitted: onChanged,
      onChanged: onChanged,
      decoration: InputDecoration(
        border: InputBorder.none,
        isDense: true,
        contentPadding: EdgeInsets.symmetric(horizontal: 6, vertical: 8),
        hintText: '',
      ),
    );
  }
}

class _DurationEditingCell extends StatefulWidget {
  final String value;
  final ValueChanged<String> onChanged;
  const _DurationEditingCell({required this.value, required this.onChanged});
  @override
  State<_DurationEditingCell> createState() => _DurationEditingCellState();
}

class _DurationEditingCellState extends State<_DurationEditingCell> {
  late String _value;
  @override
  void initState() {
    _value = widget.value;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        final initial = _parseDuration(_value);
        final picked = await showDurationPicker(
          context: context,
          initialTime: initial,
        );
        if (picked != null) {
          final newVal = _formatDuration(picked);
          setState(() => _value = newVal);
          widget.onChanged(newVal);
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 6, vertical: 8),
        child: Text(_value),
      ),
    );
  }

  Duration _parseDuration(String v) {
    if (!v.contains(":")) return Duration();
    final parts = v.split(":");
    if (parts.length != 2) return Duration();
    final h = int.tryParse(parts[0]) ?? 0;
    final m = int.tryParse(parts[1]) ?? 0;
    return Duration(hours: h, minutes: m);
  }

  String _formatDuration(Duration d) {
    final hh = d.inHours.toString().padLeft(2, '0');
    final mm = (d.inMinutes % 60).toString().padLeft(2, '0');
    return "$hh:$mm";
  }
}

class TaskDataSource extends DataGridSource {
  List<TaskModel> tasks;
  HomeCtrl ctrl;
  final Function(TaskModel, String, dynamic, HomeCtrl) updateTaskField;

  TaskDataSource({
    required this.tasks,
    required this.ctrl,
    required this.updateTaskField,
  }) {
    _buildDataGridRows();
  }

  List<DataGridRow> _dataGridRows = [];

  @override
  List<DataGridRow> get rows => _dataGridRows;

  void _buildDataGridRows() {
    _dataGridRows = tasks.map<DataGridRow>((task) {
      final customer =
          ctrl.customers.where((c) => c.docId == task.customer).isNotEmpty
          ? ctrl.customers.where((c) => c.docId == task.customer).first
          : null;
      final employee =
          ctrl.users.where((u) => u.docId == task.employee).isNotEmpty
          ? ctrl.users.where((u) => u.docId == task.employee).first
          : null;
      final supervisor =
          ctrl.users.where((u) => u.docId == task.supervisor).isNotEmpty
          ? ctrl.users.where((u) => u.docId == task.supervisor).first
          : null;
      final activity =
          ctrl.masterTasks.where((a) => a.docId == task.pType).isNotEmpty
          ? ctrl.masterTasks.where((a) => a.docId == task.pType).first
          : null;
      final benchmark = activity?.benchmark;

      return DataGridRow(
        cells: [
          DataGridCell<String>(
            columnName: 'createdAt',
            value: DateFormat('dd-MM-yyyy').format(task.createdAt),
          ),
          DataGridCell<String>(
            columnName: 'customer',
            value: customer?.name ?? '',
          ),
          DataGridCell<String>(columnName: 'details', value: task.details),
          DataGridCell<String>(
            columnName: 'employee',
            value: employee?.name ?? '',
          ),
          DataGridCell<String>(
            columnName: 'supervisor',
            value: supervisor?.name ?? '',
          ),
          DataGridCell<String>(
            columnName: 'pType',
            value: activity?.title ?? '',
          ),
          DataGridCell<String>(
            columnName: 'hours',
            value: task.actualHours ?? '',
          ),
          DataGridCell<String>(columnName: 'status', value: task.status ?? ''),
          DataGridCell<String>(columnName: 'benchmark', value: benchmark ?? ''),
          DataGridCell<String>(columnName: 'result', value: task.result ?? ''),
          DataGridCell<String>(
            columnName: 'actualHours',
            value: task.actualHours ?? '',
          ),
          DataGridCell<String>(
            columnName: 'reportSentStatus',
            value: task.reportSentStatus ?? '',
          ),
          DataGridCell<String>(
            columnName: 'customerCallStatus',
            value: task.customerCallStatus ?? '',
          ),
          DataGridCell<String>(
            columnName: 'callSummary',
            value: task.callSummary ?? '',
          ),
          DataGridCell<String>(
            columnName: 'qcFile',
            value: getQcTitle(ctrl.masterQcs, task.qcFile) ?? '',
          ),
          DataGridCell<int>(
            columnName: 'numOfErrors',
            value: int.tryParse(task.numOfErrors ?? '0') ?? 0,
          ),
          DataGridCell<String>(
            columnName: 'linksandcomments',
            value: task.linksAndComments ?? '',
          ),
          DataGridCell<TaskModel>(columnName: 'actions', value: task),
        ],
      );
    }).toList();
  }

  void updateTasks(List<TaskModel> newTasks) {
    tasks = newTasks;
    _buildDataGridRows();
    notifyListeners();
  }

  String? getQcTitle(List<Masterqcmodel> qcs, String? id) {
    if (id == null) return null;
    final qc = qcs.where((item) => item.docId == id).isNotEmpty
        ? qcs.where((item) => item.docId == id).first
        : null;
    return qc?.title;
  }

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((dataGridCell) {
        return Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.all(8.0),
          child: Text(
            dataGridCell.value.toString(),
            style: const TextStyle(fontSize: 14),
          ),
        );
      }).toList(),
    );
  }

  @override
  Future<bool> canSubmitCell(
    DataGridRow dataGridRow,
    RowColumnIndex rowColumnIndex,
    GridColumn column,
  ) async {
    // Allow editing for all editable columns
    final editableColumns = [
      'customer',
      'employee',
      'supervisor',
      'pType',
      'details',
      'status',
      'result',
      'hours',
      'actualHours',
      'reportSentStatus',
      'customerCallStatus',
      'callSummary',
      'numOfErrors',
      'linksandcomments',
    ];
    final canEdit = editableColumns.contains(column.columnName);
    print(
      'canSubmitCell called for column: ${column.columnName}, canEdit: $canEdit',
    );
    return canEdit;
  }

  @override
  Future<void> onCellSubmit(
    DataGridRow dataGridRow,
    RowColumnIndex rowColumnIndex,
    GridColumn column,
  ) async {
    final dynamic editedValue = dataGridRow
        .getCells()
        .firstWhere(
          (DataGridCell dataGridCell) =>
              dataGridCell.columnName == column.columnName,
        )
        .value;

    final rowIndex = rowColumnIndex.rowIndex - 1;
    if (rowIndex >= 0 && rowIndex < tasks.length) {
      final task = tasks[rowIndex];
      updateTaskField(task, column.columnName, editedValue, ctrl);
    }
  }

  @override
  Widget? buildEditWidget(
    DataGridRow dataGridRow,
    RowColumnIndex rowColumnIndex,
    GridColumn column,
    CellSubmit submitCell,
  ) {
    final String columnName = column.columnName;
    final rowIndex = rowColumnIndex.rowIndex - 1; // Subtract 1 for header
    print(
      'buildEditWidget called for column: $columnName, rowIndex: $rowIndex',
    );
    if (rowIndex < 0 || rowIndex >= tasks.length) return null;

    final task = tasks[rowIndex];
    String initialStr(String col) =>
        dataGridRow
            .getCells()
            .firstWhere((c) => c.columnName == col)
            .value
            ?.toString() ??
        '';

    void updateCellValue(String field, dynamic value) {
      print('updateCellValue called: field=$field, value=$value');
      // Update the task model
      updateTaskField(task, field, value, ctrl);
      // Update the cell in the row
      final cellIndex = dataGridRow.getCells().indexWhere(
        (c) => c.columnName == field,
      );
      if (cellIndex != -1) {
        dataGridRow.getCells()[cellIndex] = DataGridCell<String>(
          columnName: field,
          value: value.toString(),
        );
        print('Cell value updated in DataGridRow');
      }
      // Don't call submitCell() here - let onEditingComplete handle it
    }

    switch (columnName) {
      case 'customer':
        return SearchableDropdownCell(
          initialValue: initialStr('customer'),
          options: ctrl.customers.map((c) => c.name).toList(),
          onValueChanged: (value) {
            updateCellValue('customer', value);
          },
          onEditingComplete: () {
            submitCell();
          },
        );
      case 'employee':
        return SearchableDropdownCell(
          initialValue: initialStr('employee'),
          options: ctrl.users.map((u) => u.name).toList(),
          onValueChanged: (value) {
            updateCellValue('employee', value);
          },
          onEditingComplete: () {
            submitCell();
          },
        );
      case 'supervisor':
        return SearchableDropdownCell(
          initialValue: initialStr('supervisor'),
          options: ctrl.users.map((u) => u.name).toList(),
          onValueChanged: (value) {
            updateCellValue('supervisor', value);
          },
          onEditingComplete: () {
            submitCell();
          },
        );
      case 'pType':
        return SearchableDropdownCell(
          initialValue: initialStr('pType'),
          options: ctrl.masterTasks.map((m) => m.title).toList(),
          onValueChanged: (value) {
            updateCellValue('pType', value);
          },
          onEditingComplete: () {
            submitCell();
          },
        );
      case 'status':
        return _SimpleDropdownEditingCell(
          items: ['Pending', 'In Progress', 'Completed', 'On Hold'],
          value: initialStr('status'),
          onChanged: (v) {
            updateCellValue('status', v ?? '');
          },
        );
      case 'reportSentStatus':
        return _SimpleDropdownEditingCell(
          items: ['Not Sent', 'Sent', 'Pending'],
          value: initialStr('reportSentStatus'),
          onChanged: (v) {
            updateCellValue('reportSentStatus', v ?? '');
          },
        );
      case 'customerCallStatus':
        return _SimpleDropdownEditingCell(
          items: ['Not Called', 'Called', 'Pending'],
          value: initialStr('customerCallStatus'),
          onChanged: (v) {
            updateCellValue('customerCallStatus', v ?? '');
          },
        );
      case 'callSummary':
      case 'details':
      case 'result':
      case 'linksandcomments':
        return _SimpleTextEditingCell(
          value: initialStr(columnName),
          onChanged: (v) {
            updateCellValue(columnName, v);
          },
        );
      case 'hours':
        return _DurationEditingCell(
          value: initialStr('hours'),
          onChanged: (v) {
            updateCellValue('hours', v);
          },
        );
      case 'numOfErrors':
        return _SimpleTextEditingCell(
          value: initialStr('numOfErrors'),
          onChanged: (v) {
            updateCellValue('numOfErrors', v);
          },
          keyboardType: TextInputType.number,
        );
      default:
        return super.buildEditWidget(
          dataGridRow,
          rowColumnIndex,
          column,
          submitCell,
        );
    }
  }
}

class _CustomerEditDialog extends StatefulWidget {
  final String initialValue;
  final List<Customermodel> customers;
  final Function(String) onChanged;

  const _CustomerEditDialog({
    required this.initialValue,
    required this.customers,
    required this.onChanged,
  });

  @override
  State<_CustomerEditDialog> createState() => _CustomerEditDialogState();
}

class _CustomerEditDialogState extends State<_CustomerEditDialog> {
  late TextEditingController _controller;
  List<Customermodel> _filteredCustomers = [];
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _removeOverlay();
    _controller.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final query = _controller.text.toLowerCase().trim();

    if (query.isEmpty) {
      _filteredCustomers = [];
      _removeOverlay();
    } else {
      _filteredCustomers = widget.customers.where((customer) {
        return customer.name.toLowerCase().contains(query) ||
            customer.companyName.toLowerCase().contains(query) ||
            (customer.email?.toLowerCase().contains(query) ?? false);
      }).toList();

      if (_filteredCustomers.isNotEmpty) {
        _showOverlay();
      } else {
        _removeOverlay();
      }
    }
  }

  void _showOverlay() {
    _removeOverlay();

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: 300,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0, 35),
          child: Material(
            elevation: 4,
            child: Container(
              constraints: BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(4),
              ),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _filteredCustomers.length,
                itemBuilder: (context, index) {
                  final customer = _filteredCustomers[index];
                  return ListTile(
                    dense: true,
                    title: Text(customer.name, style: TextStyle(fontSize: 14)),
                    subtitle: Text(
                      customer.companyName,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    trailing: customer.email?.isNotEmpty == true
                        ? Text(
                            customer.email!,
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey.shade500,
                            ),
                          )
                        : null,
                    onTap: () {
                      _controller.text = customer.name;
                      widget.onChanged(customer.name);
                      _removeOverlay();
                      Navigator.of(context).pop();
                    },
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );

    if (mounted) {
      Overlay.of(context).insert(_overlayEntry!);
    }
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Edit Customer'),
      content: SizedBox(
        width: 300,
        child: CompositedTransformTarget(
          link: _layerLink,
          child: TextField(
            controller: _controller,
            autofocus: true,
            decoration: InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Customer Name',
              hintText: 'Type to search customers...',
            ),
            onSubmitted: (value) {
              widget.onChanged(value);
              Navigator.of(context).pop();
            },
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onChanged(_controller.text);
            Navigator.of(context).pop();
          },
          child: Text('Save'),
        ),
      ],
    );
  }
}
