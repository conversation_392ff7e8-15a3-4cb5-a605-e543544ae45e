import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/models/customermodel.dart';
import 'package:legacy_pms/models/masterqcmodel.dart';
import 'package:legacy_pms/models/taskmodel.dart';
import 'package:legacy_pms/models/usermodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/firebase.dart';
import 'package:legacy_pms/shared/methods.dart';

class QcFormDialog extends StatefulWidget {
  final Masterqcmodel masterQc;
  final String taskDocId;
  final String userUid;
  final HomeCtrl ctrl;

  const QcFormDialog({
    super.key,
    required this.masterQc,
    required this.taskDocId,
    required this.userUid,
    required this.ctrl,
  });

  @override
  State<QcFormDialog> createState() => _QcFormDialogState();
}

class _QcFormDialogState extends State<QcFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final Map<String, dynamic> _formValues = {};
  final Map<String, dynamic> _tlFormValues = {}; // TL's review responses
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, TextEditingController> _tlControllers = {};

  // Form and role states
  bool _isSubmitted = false;
  bool _isInReworkMode = false;
  bool _isLoading = true;
  bool _isEmployee = false;
  bool _isTL = false;
  bool _isAdmin = false;
  bool _taskCompleted = false;
  TaskModel? _currentTask;

  // Loader states for async operations
  late bool _isSaving;
  late bool _isSubmitting;
  late bool _isSendingForRework;
  late bool _isLoadingData;

  // Permission flags
  bool _canEditTask = false;

  @override
  void initState() {
    super.initState();
    // Initialize loader states
    _isSaving = false;
    _isSubmitting = false;
    _isSendingForRework = false;
    _isLoadingData = true;
    _loadExistingAnswers();
  }

  @override
  void dispose() {
    for (final c in _controllers.values) {
      c.dispose();
    }
    for (final c in _tlControllers.values) {
      c.dispose();
    }
    super.dispose();
  }

  /// Load existing data from Firestore (prefill if available)
  Future<void> _loadExistingAnswers() async {
    setState(() {
      _isLoadingData = true;
    });

    try {
      // Load task data to determine user role
      final taskDoc = await FBFireStore.tasks.doc(widget.taskDocId).get();
      final taskData = taskDoc.data();

      if (taskData != null) {
        _currentTask = TaskModel.fromJson({
          ...taskData,
          'docId': widget.taskDocId,
        });

        // Determine user roles and permissions
        await _determineUserRoleAndPermissions();
        _taskCompleted = _currentTask!.completed ?? false;

        final inputs = widget.masterQc.qcInputModel ?? [];

        // Load existing answers if available
        if (taskData['answers'] != null) {
          final Map<String, dynamic> answers = Map<String, dynamic>.from(
            taskData['answers'],
          );

          // Load employee answers
          final employeeAnswers = Map<String, dynamic>.from(
            answers[_currentTask!.employee] ?? {},
          );
          if (employeeAnswers.isNotEmpty) {
            _isInReworkMode = employeeAnswers['isInReworkMode'] ?? false;

            // For employees, check if they have submitted
            if (_isEmployee) {
              _isSubmitted =
                  !_isInReworkMode &&
                  employeeAnswers.containsKey('completedAt');
            }

            employeeAnswers.forEach((key, value) {
              if (key != 'isInReworkMode' && key != 'completedAt') {
                _formValues[key] = value;
              }
            });
          }

          // Load TL answers if TL is viewing
          if (_isTL) {
            final tlAnswers = Map<String, dynamic>.from(
              answers[_currentTask!.supervisor] ?? {},
            );
            if (tlAnswers.isNotEmpty) {
              tlAnswers.forEach((key, value) {
                if (key != 'isInReworkMode' && key != 'completedAt') {
                  _tlFormValues[key] = value;
                }
              });
            }

            // For TL, check if task is completed or if employee has submitted
            final employeeSubmitted =
                employeeAnswers.containsKey('completedAt') && !_isInReworkMode;
            _isSubmitted = _taskCompleted || employeeSubmitted;
          }
        }

        // Initialize form values for all inputs
        for (final item in inputs.whereType<Map<String, dynamic>>()) {
          final title = item['title'] ?? '';
          final type = item['type'] ?? 'textfield';
          final key = title.toLowerCase().trim();

          // Initialize employee form values
          if (!_formValues.containsKey(key)) {
            _formValues[key] = type == 'yesno' ? false : '';
          }

          // Initialize TL form values if TL
          if (_isTL && !_tlFormValues.containsKey(key)) {
            _tlFormValues[key] = type == 'yesno' ? false : '';
          }
        }
      }

      setState(() {
        _isLoading = false;
        _isLoadingData = false;
      });
    } catch (e) {
      debugPrint('❌ Error loading QC answers: $e');
      setState(() {
        _isLoading = false;
        _isLoadingData = false;
      });
    }
  }

  /// Determine user role and permissions
  Future<void> _determineUserRoleAndPermissions() async {
    if (_currentTask == null) return;

    // Determine basic roles
    _isEmployee = _currentTask!.employee == widget.userUid;
    _isTL = _currentTask!.supervisor == widget.userUid;

    // Check if user is admin
    final currentUser = widget.ctrl.loggedInUser;
    if (currentUser != null) {
      _isAdmin =
          currentUser.role.toLowerCase().contains('admin') ||
          currentUser.role.toLowerCase().contains('hr');

      // Get user permissions
      final userPermissions = widget.ctrl.userRoles;
      _canEditTask =
          userPermissions.contains(Permissions.canEditTask) || _isAdmin;
    }
  }

  /// Handle Submit - Role-based submission logic with enhanced permissions and loaders
  Future<void> handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      showSnackBar('Please fill all required fields before submitting.');
      return;
    }

    // Check permissions for non-admin users
    if (!_isAdmin && !_canEditTask && _isTL) {
      showSnackBar('You do not have permission to edit this task.');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      if (_isEmployee || _isAdmin) {
        // Employee submitting QC form for TL review (Admin can act as employee)
        await _handleEmployeeSubmit();
      } else if (_isTL || _isAdmin) {
        // TL completing the task after review (Admin can act as TL)
        await _handleTLSubmit();
      }
    } catch (e) {
      debugPrint('❌ Error in handleSubmit: $e');
      showSnackBar('An error occurred. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  /// Handle employee submission
  Future<void> _handleEmployeeSubmit() async {
    final success = await _saveEmployeeAnswers();
    if (!success) return;

    // Send email to TL for quality check
    await _sendQCReadyEmail();

    showSnackBar('QC form submitted successfully! TL has been notified.');
    if (mounted) Navigator.of(context).pop();
  }

  /// Handle TL submission (task completion)
  Future<void> _handleTLSubmit() async {
    final success = await _saveTLAnswers();
    if (!success) return;

    // Update task status to completed
    await _updateTaskCompletion();

    // Send completion email to employee
    await _sendTaskCompletionEmail();

    showSnackBar('Task marked as completed! Employee has been notified.');
    if (mounted) Navigator.of(context).pop();
  }

  /// Handle Send for Rework - TL sending task back to employee with enhanced permissions
  Future<void> handleSendForRework() async {
    // Check permissions for non-admin users
    if (!_isAdmin && !_canEditTask) {
      showSnackBar('You do not have permission to send tasks for rework.');
      return;
    }

    setState(() {
      _isSendingForRework = true;
    });

    try {
      // Save TL's feedback
      final success = await _saveTLAnswers(isRework: true);
      if (!success) return;

      // Send rework email to employee
      await _sendReworkRequestEmail();

      showSnackBar('Rework request sent! Employee has been notified.');
      if (mounted) Navigator.of(context).pop();
    } catch (e) {
      debugPrint('❌ Error in handleSendForRework: $e');
      showSnackBar('An error occurred. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isSendingForRework = false;
        });
      }
    }
  }

  /// Save employee answers to Firestore with loader state
  Future<bool> _saveEmployeeAnswers() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final taskDoc = FBFireStore.tasks.doc(widget.taskDocId);
      final doc = await taskDoc.get();
      final data = doc.data();

      Map<String, dynamic> existingAnswers = {};
      if (data != null && data['answers'] != null) {
        existingAnswers = Map<String, dynamic>.from(data['answers']);
      }

      final normalized = <String, dynamic>{};
      _formValues.forEach((key, value) {
        normalized[key.toLowerCase().trim()] = value;
      });

      existingAnswers[_currentTask!.employee] = {
        ...normalized,
        'completedAt': FieldValue.serverTimestamp(),
        'isInReworkMode': false,
      };

      await taskDoc.set({'answers': existingAnswers}, SetOptions(merge: true));
      return true;
    } catch (e) {
      debugPrint('❌ Failed to save employee QC answers: $e');
      showSnackBar('Failed to save QC answers');
      return false;
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// Save TL answers to Firestore with loader state
  Future<bool> _saveTLAnswers({bool isRework = false}) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final taskDoc = FBFireStore.tasks.doc(widget.taskDocId);
      final doc = await taskDoc.get();
      final data = doc.data();

      Map<String, dynamic> existingAnswers = {};
      if (data != null && data['answers'] != null) {
        existingAnswers = Map<String, dynamic>.from(data['answers']);
      }

      final normalized = <String, dynamic>{};
      _tlFormValues.forEach((key, value) {
        normalized[key.toLowerCase().trim()] = value;
      });

      existingAnswers[_currentTask!.supervisor] = {
        ...normalized,
        'completedAt': FieldValue.serverTimestamp(),
        'isInReworkMode': false,
      };

      // If rework, update employee's rework status
      if (isRework && existingAnswers.containsKey(_currentTask!.employee)) {
        existingAnswers[_currentTask!.employee]['isInReworkMode'] = true;
      }

      await taskDoc.set({'answers': existingAnswers}, SetOptions(merge: true));
      return true;
    } catch (e) {
      debugPrint('❌ Failed to save TL QC answers: $e');
      showSnackBar('Failed to save QC answers');
      return false;
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// Update task completion status
  Future<void> _updateTaskCompletion() async {
    try {
      await FBFireStore.tasks.doc(widget.taskDocId).update({
        'status': TaskStatus.completed,
        'completed': true,
        'completedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('❌ Failed to update task completion: $e');
      showSnackBar('Failed to update task status');
    }
  }

  /// Send QC ready email to TL
  Future<void> _sendQCReadyEmail() async {
    try {
      if (_currentTask == null) return;

      // Get TL details
      final tlDoc = await FBFireStore.users.doc(_currentTask!.supervisor).get();
      if (!tlDoc.exists) return;
      final tl = Usermodel.fromSnap(tlDoc);

      // Get employee details
      final employeeDoc = await FBFireStore.users
          .doc(_currentTask!.employee)
          .get();
      if (!employeeDoc.exists) return;
      final employee = Usermodel.fromSnap(employeeDoc);

      // Get customer details
      final customerDoc = await FBFireStore.customers
          .doc(_currentTask!.customer)
          .get();
      if (!customerDoc.exists) return;
      final customer = Customermodel.fromSnap(customerDoc);

      final callable = FBFunctions.ff.httpsCallable('sendQCReadyEmail');
      await callable.call({
        'taskDetails': _currentTask!.details,
        'taskId': widget.taskDocId,
        'tlEmail': tl.email,
        'tlName': tl.name,
        'employeeName': employee.name,
        'customerName': customer.name,
      });
    } catch (e) {
      debugPrint('❌ Failed to send QC ready email: $e');
    }
  }

  /// Send task completion email to employee
  Future<void> _sendTaskCompletionEmail() async {
    try {
      if (_currentTask == null) return;

      // Get employee details
      final employeeDoc = await FBFireStore.users
          .doc(_currentTask!.employee)
          .get();
      if (!employeeDoc.exists) return;
      final employee = Usermodel.fromSnap(employeeDoc);

      // Get customer details
      final customerDoc = await FBFireStore.customers
          .doc(_currentTask!.customer)
          .get();
      if (!customerDoc.exists) return;
      final customer = Customermodel.fromSnap(customerDoc);

      final callable = FBFunctions.ff.httpsCallable('sendTaskCompletionEmail');
      await callable.call({
        'taskDetails': _currentTask!.details,
        'taskId': widget.taskDocId,
        'employeeEmail': employee.email,
        'employeeName': employee.name,
        'customerName': customer.name,
      });
    } catch (e) {
      debugPrint('❌ Failed to send task completion email: $e');
    }
  }

  /// Send rework request email to employee
  Future<void> _sendReworkRequestEmail() async {
    try {
      if (_currentTask == null) return;

      // Get employee details
      final employeeDoc = await FBFireStore.users
          .doc(_currentTask!.employee)
          .get();
      if (!employeeDoc.exists) return;
      final employee = Usermodel.fromSnap(employeeDoc);

      // Get customer details
      final customerDoc = await FBFireStore.customers
          .doc(_currentTask!.customer)
          .get();
      if (!customerDoc.exists) return;
      final customer = Customermodel.fromSnap(customerDoc);

      // Collect feedback from TL form values
      String feedback = '';
      _tlFormValues.forEach((key, value) {
        if (value != null && value.toString().isNotEmpty) {
          feedback += '$key: $value\n';
        }
      });

      final callable = FBFunctions.ff.httpsCallable('sendReworkRequestEmail');
      await callable.call({
        'taskDetails': _currentTask!.details,
        'taskId': widget.taskDocId,
        'employeeEmail': employee.email,
        'employeeName': employee.name,
        'customerName': customer.name,
        'feedback': feedback.isNotEmpty ? feedback : null,
      });
    } catch (e) {
      debugPrint('❌ Failed to send rework request email: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final inputs = widget.masterQc.qcInputModel ?? [];

    return AlertDialog(
      backgroundColor: popupBgColor,
      titlePadding: const EdgeInsets.fromLTRB(24, 20, 24, 10),
      contentPadding: const EdgeInsets.fromLTRB(24, 0, 24, 10),
      actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            widget.masterQc.title.toUpperCase(),
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: _isSubmitted ? Colors.green : Colors.orangeAccent,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ),
      content: _isLoading || _isLoadingData
          ? const Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading QC form...'),
                ],
              ),
            )
          : Container(
              width: MediaQuery.of(context).size.width * 0.8,
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.7,
              ),
              child: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      // Admin access indicator
                      if (_isAdmin)
                        Container(
                          width: double.infinity,
                          margin: const EdgeInsets.only(bottom: 12),
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Colors.purple.shade100,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.purple.shade300),
                          ),
                          child: const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.admin_panel_settings,
                                color: Colors.purple,
                                size: 20,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Admin Access - Full Control',
                                style: TextStyle(
                                  color: Colors.purple,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      // Rework mode indicator
                      if (_isInReworkMode)
                        Container(
                          width: double.infinity,
                          margin: const EdgeInsets.only(bottom: 12),
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.warning_amber_rounded,
                                color: Colors.orange,
                                size: 20,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Rework Mode - Editable',
                                style: TextStyle(
                                  color: Colors.orange,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      // Permission restrictions indicator for non-admin users
                      if (!_isAdmin && !_canEditTask && (_isTL || _isEmployee))
                        Container(
                          width: double.infinity,
                          margin: const EdgeInsets.only(bottom: 12),
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: Colors.red.shade100,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red.shade300),
                          ),
                          child: const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.lock, color: Colors.red, size: 20),
                              SizedBox(width: 8),
                              Text(
                                'Read-Only Access - No Edit Permission',
                                style: TextStyle(
                                  color: Colors.red,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      if (inputs.isEmpty)
                        const Text(
                          'No QC inputs available.',
                          style: TextStyle(
                            color: Colors.grey,
                            fontStyle: FontStyle.italic,
                          ),
                        )
                      else ...[
                        // Employee section (always visible)
                        if (_isTL) ...[
                          Container(
                            width: double.infinity,
                            margin: const EdgeInsets.only(bottom: 16),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.blue.shade200),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Employee Responses',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                    color: Colors.blue.shade700,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                ...inputs.whereType<Map<String, dynamic>>().map(
                                  (item) => Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 4.0,
                                    ),
                                    child: _buildEmployeeReadOnlyField(item),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // TL Review section
                          Container(
                            width: double.infinity,
                            margin: const EdgeInsets.only(bottom: 16),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.green.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.green.shade200),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'TL Review & Feedback',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                    color: Colors.green.shade700,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                ...inputs.whereType<Map<String, dynamic>>().map(
                                  (item) => Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 4.0,
                                    ),
                                    child: _buildTLReviewField(item),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ] else
                          // Employee view - normal form fields
                          ...inputs.whereType<Map<String, dynamic>>().map(
                            (item) => Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 8.0,
                              ),
                              child: _buildFormField(item),
                            ),
                          ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
      actions: [
        TextButton(
          onPressed: _isSaving || _isSubmitting || _isSendingForRework
              ? null
              : () => Navigator.of(context).pop(),
          child: Text(_taskCompleted || _isSubmitted ? 'Close' : 'Cancel'),
        ),
        // Employee role: Submit button (Admin can also act as employee)
        if ((_isEmployee || _isAdmin) && !_isSubmitted && !_taskCompleted)
          ElevatedButton(
            onPressed: _isSaving || _isSubmitting || _isSendingForRework
                ? null
                : () => handleSubmit(),
            style: ElevatedButton.styleFrom(
              backgroundColor: logoTealColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isSubmitting
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('Submit'),
          ),
        // TL role: Send for Rework button (Admin can also act as TL)
        if ((_isTL || _isAdmin) &&
            !_taskCompleted &&
            _isSubmitted &&
            (_canEditTask || _isAdmin))
          ElevatedButton(
            onPressed: _isSaving || _isSubmitting || _isSendingForRework
                ? null
                : () => handleSendForRework(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orangeAccent,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isSendingForRework
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('Send for Rework'),
          ),
        // TL role: Submit button (Admin can also act as TL)
        if ((_isTL || _isAdmin) &&
            !_taskCompleted &&
            _isSubmitted &&
            (_canEditTask || _isAdmin))
          ElevatedButton(
            onPressed: _isSaving || _isSubmitting || _isSendingForRework
                ? null
                : () => handleSubmit(),
            style: ElevatedButton.styleFrom(
              backgroundColor: logoTealColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isSubmitting
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('Submit'),
          ),
      ],
    );
  }

  Widget _buildFormField(Map<String, dynamic> item) {
    final title = item['title'] ?? '';
    final type = item['type'] ?? 'textfield';
    final key = title.toLowerCase().trim();
    final displayTitle = title.isNotEmpty
        ? title[0].toUpperCase() + title.substring(1)
        : '';

    if (type == 'yesno') {
      return Row(
        children: [
          Expanded(
            child: Text(
              displayTitle,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
          ),
          Checkbox(
            value: _formValues[key] ?? false,
            onChanged:
                (_isSubmitted ||
                    _isSaving ||
                    (!_isAdmin && !_canEditTask && !_isInReworkMode))
                ? null
                : (val) => setState(() {
                    _formValues[key] = val ?? false;
                  }),
          ),
        ],
      );
    } else {
      _controllers.putIfAbsent(
        key,
        () => TextEditingController(text: _formValues[key] ?? ''),
      );

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            displayTitle,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const SizedBox(height: 4),
          TextFormField(
            controller: _controllers[key],
            enabled:
                !_isSubmitted &&
                !_isSaving &&
                (_isAdmin || _canEditTask || _isInReworkMode),
            decoration: InputDecoration(
              border: const OutlineInputBorder(),
              isDense: true,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 10,
              ),
              suffixIcon: _isSaving
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: Padding(
                        padding: EdgeInsets.all(12.0),
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : null,
            ),
            maxLines: 2,
            validator: (val) {
              if (!_isSubmitted && (val == null || val.trim().isEmpty)) {
                return 'Required';
              }
              return null;
            },
            onChanged: (val) => _formValues[key] = val,
          ),
        ],
      );
    }
  }

  /// Build read-only field for employee responses (TL view)
  Widget _buildEmployeeReadOnlyField(Map<String, dynamic> item) {
    final title = item['title'] ?? '';
    final type = item['type'] ?? 'textfield';
    final key = title.toLowerCase().trim();
    final displayTitle = title.isNotEmpty
        ? title[0].toUpperCase() + title.substring(1)
        : '';
    final value = _formValues[key];

    if (type == 'yesno') {
      return Row(
        children: [
          Expanded(
            child: Text(
              displayTitle,
              style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
            ),
          ),
          Icon(
            value == true ? Icons.check_box : Icons.check_box_outline_blank,
            color: value == true ? Colors.green : Colors.grey,
          ),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            displayTitle,
            style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
          ),
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(4),
              color: Colors.grey.shade50,
            ),
            child: Text(
              value?.toString() ?? 'No response',
              style: TextStyle(
                color: value?.toString().isNotEmpty == true
                    ? Colors.black87
                    : Colors.grey.shade600,
                fontStyle: value?.toString().isNotEmpty == true
                    ? FontStyle.normal
                    : FontStyle.italic,
              ),
            ),
          ),
        ],
      );
    }
  }

  /// Build TL review field
  Widget _buildTLReviewField(Map<String, dynamic> item) {
    final title = item['title'] ?? '';
    final type = item['type'] ?? 'textfield';
    final key = title.toLowerCase().trim();
    final displayTitle = title.isNotEmpty
        ? title[0].toUpperCase() + title.substring(1)
        : '';

    if (type == 'yesno') {
      return Row(
        children: [
          Expanded(
            child: Text(
              '$displayTitle (Review)',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
            ),
          ),
          Checkbox(
            value: _tlFormValues[key] ?? false,
            onChanged:
                (_taskCompleted || _isSaving || (!_isAdmin && !_canEditTask))
                ? null
                : (val) => setState(() {
                    _tlFormValues[key] = val ?? false;
                  }),
          ),
        ],
      );
    } else {
      _tlControllers.putIfAbsent(
        key,
        () => TextEditingController(text: _tlFormValues[key] ?? ''),
      );

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$displayTitle (Review)',
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
          ),
          const SizedBox(height: 4),
          TextFormField(
            controller: _tlControllers[key],
            enabled:
                !_taskCompleted && !_isSaving && (_isAdmin || _canEditTask),
            decoration: InputDecoration(
              border: const OutlineInputBorder(),
              isDense: true,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 10,
              ),
              hintText: 'Add your review/feedback...',
              suffixIcon: _isSaving
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: Padding(
                        padding: EdgeInsets.all(12.0),
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : null,
            ),
            maxLines: 2,
            onChanged: (val) => _tlFormValues[key] = val,
          ),
        ],
      );
    }
  }
}
