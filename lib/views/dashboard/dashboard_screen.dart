import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/views/dashboard/admin_dashboard_page.dart';
import 'package:legacy_pms/views/dashboard/user_dashboard_page.dart';

class CommonDashboardScreen extends StatefulWidget {
  const CommonDashboardScreen({super.key});

  @override
  State<CommonDashboardScreen> createState() => _CommonDashboardScreenState();
}

class _CommonDashboardScreenState extends State<CommonDashboardScreen> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final isAdmin = ctrl.loggedInUser?.role == admin;
        return isAdmin ? const AdminDashboardPage() : const UserDashboardPage();
      },
    );
  }
}
