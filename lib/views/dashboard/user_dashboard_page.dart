import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/views/dashboard/admin_dashboard_page.dart';

class UserDashboardPage extends StatefulWidget {
  const UserDashboardPage({super.key});

  @override
  State<UserDashboardPage> createState() => _UserDashboardPageState();
}

class _UserDashboardPageState extends State<UserDashboardPage> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final currentUserUid = ctrl.loggedInUser?.docId;

        final userTasks = ctrl.tasks
            .where(
              (task) =>
                  task.customer == currentUserUid ||
                  task.employee == currentUserUid ||
                  task.supervisor == currentUserUid,
            )
            .toList();

        final myTasks = userTasks
            .where((t) => t.status != TaskStatus.completed)
            .map(
              (task) => TaskRow(
                name: task.details,
                status: task.status.toString(),
                dueDate: task.actualHours.toString(),
              ),
            )
            .toList();

        final overdueTasks = userTasks
            .where((t) => t.status.toString() == TaskStatus.delayed)
            .map(
              (task) => TaskRow(
                name: task.details,
                status: task.status.toString(),
                dueDate: task.actualHours.toString(),
              ),
            )
            .toList();

        final completedTasks = userTasks
            .where((t) => t.status.toString() == TaskStatus.completed)
            .map(
              (task) => TaskRow(
                name: task.details,
                status: task.status.toString(),
                dueDate: task.actualHours.toString(),
              ),
            )
            .toList();

        return Scaffold(
          backgroundColor: Colors.white,
          body: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(18),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // DashboardTableCard(
                  //   ctrl: ctrl,
                  //   title: "Tasks (${myTasks.length})",
                  //   rows: myTasks,
                  // ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Flexible(
                        flex: 1,
                        child: DashboardTableCard(
                          ctrl: ctrl,
                          title: "My overdue tasks (${overdueTasks.length})",
                          rows: overdueTasks,
                        ),
                      ),
                      const SizedBox(width: 18),
                      Flexible(
                        flex: 1,
                        child: DashboardTableCard(
                          ctrl: ctrl,
                          title:
                              "My completed tasks (${completedTasks.length})",
                          rows: completedTasks,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
