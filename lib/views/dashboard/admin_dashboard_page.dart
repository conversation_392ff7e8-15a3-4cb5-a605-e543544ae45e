import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/models/customermodel.dart';
import 'package:legacy_pms/models/taskmodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/router.dart';

class AdminDashboardPage extends StatelessWidget {
  const AdminDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    // List<TaskRow> myTasks = [];
    List<TaskRow> overdueTasks = [];
    List<TaskRow> completedTasks = [];

    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final currentUser = ctrl.loggedInUser;
        final currentUserUid = currentUser?.docId;

        // final nonAdminUsers = ctrl.users.where((u) => u.role != admin).toList();

        final mastertasks = ctrl.masterTasks
            .where((task) => task.showCount == true)
            .toList();

        // print("mastertasks.showCount is true : ${mastertasks.length}");

        final userTasks = ctrl.tasks
            .where(
              (task) =>
                  task.customer == currentUserUid ||
                  task.employee == currentUserUid ||
                  task.supervisor == currentUserUid,
              // task.qualityChecker == currentUserUid,
            )
            .toList();

        List<TaskRow> myTasks = ctrl.tasks
            .where((element) => element.status != TaskStatus.completed)
            .map(
              (task) => TaskRow(
                name: task.details,
                status: task.status.toString(),
                dueDate: task.actualHours.toString(),
              ),
            )
            .toList();

        overdueTasks = userTasks
            .where((task) => task.status.toString() == TaskStatus.delayed)
            .map(
              (task) => TaskRow(
                name: task.details,
                status: task.status.toString(),
                dueDate: task.actualHours.toString(),
              ),
            )
            .toList();

        completedTasks = userTasks
            .where((task) => task.status.toString() == TaskStatus.completed)
            .map(
              (task) => TaskRow(
                name: task.details,
                status: task.status.toString(),
                dueDate: task.actualHours.toString(),
              ),
            )
            .toList();

        int unassignedClientsCount = 0;
        int unassignedEmployeesCount = 0;
        int tasksDelayedCount = 0;
        int tasksNotStartedCount = 0;
        int absentEmployeesCount = 0;

        // Assuming ctrl has these lists: tasks, customers, users (employees & clients)

        // Unassigned Clients: customers who do NOT have any task assigned
        final customersWithTasks = ctrl.tasks.map((t) => t.customer).toSet();
        unassignedClientsCount = ctrl.customers
            .where((c) => !customersWithTasks.contains(c.docId))
            .length;

        // Unassigned Employees: employees who do NOT have any task assigned
        final employeesWithTasks = ctrl.tasks.map((t) => t.employee).toSet();
        unassignedEmployeesCount = ctrl.users
            .where(
              (u) => u.role != admin && !employeesWithTasks.contains(u.docId),
            )
            .length;

        // Tasks Delayed: count of tasks with status = delayed
        tasksDelayedCount = ctrl.tasks
            .where((t) => t.status == TaskStatus.delayed)
            .length;

        // Tasks Not Started Yet: count of tasks with status = notStarted (adjust per your enum/string)
        tasksNotStartedCount = ctrl.tasks
            .where(
              (t) => t.status == TaskStatus.notYetStarted,
            ) // replace with actual status value
            .length;

        // Absent Employees: count of employees marked absent (assuming a 'status' or 'absent' flag)
        absentEmployeesCount = ctrl.users
            .where(
              (u) =>
                  u.role != admin && (u.status == 'absent' || u.status == true),
            )
            .length;

        // final mediaQuery = MediaQuery.of(context);
        // final double topPadding = mediaQuery.padding.top + kToolbarHeight;

        return SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 18),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 20),
              ctrl.loggedInUser?.role == admin
                  ? Row(
                      children: [
                        Flexible(
                          child: DashboardStatCard(
                            title: "Unassigned Clients",
                            value: "$unassignedClientsCount",
                          ),
                        ),
                        SizedBox(width: 18),
                        Flexible(
                          child: DashboardStatCard(
                            title: "Unassigned Employees",
                            value: "$unassignedEmployeesCount",
                          ),
                        ),
                        SizedBox(width: 18),
                        Flexible(
                          child: DashboardStatCard(
                            title: "Tasks Delayed",
                            value: "$tasksDelayedCount",
                          ),
                        ),

                        SizedBox(width: 18),
                        Flexible(
                          child: DashboardStatCard(
                            title: "Tasks not started yet",
                            value: "$tasksNotStartedCount",
                          ),
                        ),

                        SizedBox(width: 18),
                        Flexible(
                          child: DashboardStatCard(
                            title: "Absent Employees",
                            value: "$absentEmployeesCount",
                          ),
                        ),
                      ],
                    )
                  : SizedBox.shrink(),
              ctrl.loggedInUser?.role == admin
                  ? SizedBox(height: 24)
                  : SizedBox.shrink(),

              // DashboardTableCard(
              //   ctrl: ctrl,
              //   title: "Tasks (${myTasks.length})",
              //   rows: myTasks,
              // ),
              const SizedBox(height: 24),

              Row(
                children: [
                  Flexible(
                    flex: 1,
                    child: DashboardTableCard(
                      ctrl: ctrl,
                      title: "My overdue tasks (${overdueTasks.length})",
                      rows: overdueTasks,
                    ),
                  ),
                  const SizedBox(width: 18),
                  Flexible(
                    flex: 1,
                    child: DashboardTableCard(
                      ctrl: ctrl,
                      title: "My completed tasks (${completedTasks.length})",
                      rows: completedTasks,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              StaggeredGrid.count(
                crossAxisCount: 5,
                crossAxisSpacing: 18,
                mainAxisSpacing: 18,
                children: [
                  ...List.generate(
                    mastertasks.length,
                    (index) => DashboardStatCard(
                      inkwellOnTap: () {
                        showRemainingCustomersPopup(
                          context,
                          mastertasks[index].docId,
                          ctrl.tasks,
                          ctrl.customers,
                          (selectedCustomer) {
                            // Navigator.of(context).pop();
                            context.go(
                              '${Routes.customerdetails}/${selectedCustomer.docId}',
                            );
                            // Navigator.of(context).pop();
                          },
                        );
                      },
                      title: mastertasks[index].title,
                      value:
                          "${getCountForMasterTask(mastertasks[index].docId, ctrl.tasks, ctrl.customers.length)}",
                    ),
                  ),
                ],
              ),
              SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  int getCountForMasterTask(
    String masterTaskId,
    List<TaskModel> allTasks,
    int totalCustomers,
  ) {
    final linkedCustomers = allTasks
        .where((task) => task.pType == masterTaskId)
        .map((task) => task.customer)
        .toSet();

    final remaining = totalCustomers - linkedCustomers.length;

    return remaining >= 0 ? remaining : 0;
  }

  void showRemainingCustomersPopup(
    BuildContext context,
    String masterTaskId,
    List<TaskModel> allTasks,
    List<Customermodel> allCustomers,
    void Function(Customermodel) onCustomerTap,
  ) {
    // Customers linked to this master task
    final linkedCustomers = allTasks
        .where((task) => task.pType == masterTaskId)
        .map((task) => task.customer)
        .toSet();

    // Customers NOT linked (remaining)
    final remainingCustomers = allCustomers
        .where((customer) => !linkedCustomers.contains(customer.docId))
        .toList();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: popupBgColor,
        title: Text('Customers NOT linked to Master Task'),
        content: Container(
          width: double.maxFinite,
          constraints: BoxConstraints(maxHeight: 400, maxWidth: 400),
          child: remainingCustomers.isNotEmpty
              ? ListView.separated(
                  shrinkWrap: true,
                  itemCount: remainingCustomers.length,
                  separatorBuilder: (_, __) => Divider(height: 1),
                  itemBuilder: (context, index) {
                    final customer = remainingCustomers[index];
                    return ListTile(
                      onTap: () {
                        Navigator.of(context).pop(); // Close the dialog
                        onCustomerTap(customer); // Trigger callback
                      },
                      title: Text(
                        customer.name,
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (customer.companyName.isNotEmpty)
                            Text(customer.companyName),
                          Row(
                            children: [
                              if (customer.clientType.isNotEmpty)
                                Text('Type: ${customer.clientType}'),
                              SizedBox(width: 8),
                              if (customer.phoneNo != null)
                                Text('Phone: ${customer.phoneNo}'),
                            ],
                          ),
                          if (customer.email != null)
                            Text('Email: ${customer.email}'),
                        ],
                      ),
                      leading: CircleAvatar(
                        child: Text(
                          customer.name.isNotEmpty
                              ? customer.name.substring(0, 1).toUpperCase()
                              : '?',
                        ),
                      ),
                    );
                  },
                )
              : Center(child: Text('All customers have this task assigned.')),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

class DashboardStatCard extends StatelessWidget {
  final String title;
  final String value;
  final void Function()? inkwellOnTap;

  const DashboardStatCard({
    super.key,
    required this.title,
    required this.value,
    this.inkwellOnTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      hoverColor: Colors.transparent,
      splashColor: Colors.transparent,
      focusColor: Colors.transparent,
      overlayColor: WidgetStateProperty.all(Colors.transparent),
      highlightColor: Colors.transparent,
      onTap: inkwellOnTap,
      child: Container(
        height: 170,
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: const Color(0xffe3e6e8), width: 1.4),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.03),
              blurRadius: 8,
              spreadRadius: 1,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                value,
                style: Theme.of(context).textTheme.displaySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: 32,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class DashboardTableCard extends StatelessWidget {
  final String title;
  final List<TaskRow> rows;
  final HomeCtrl ctrl;

  const DashboardTableCard({
    super.key,
    required this.title,
    required this.rows,
    required this.ctrl,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      width: double.infinity,
      height: ctrl.loggedInUser?.role == admin
          ? 220
          : 320, // increased height for scroll
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xffe3e6e8), width: 1.2),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.035),
            blurRadius: 10,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          // Table header row
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 8),
            color: const Color(0xfff6f7fb),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    "Name",
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[700],
                      fontSize: 15,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    "Status",
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.grey,
                      fontSize: 15,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    "Due",
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.grey,
                      fontSize: 15,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1, thickness: 1, color: Color(0xffe3e6e8)),
          // Table rows - scrollable container
          Expanded(
            child: rows.isNotEmpty
                ? ListView.separated(
                    padding: const EdgeInsets.symmetric(vertical: 0),
                    itemCount: rows.length,
                    separatorBuilder: (_, __) => Divider(
                      color: Colors.grey[200],
                      height: 1,
                      thickness: 1,
                    ),
                    itemBuilder: (context, index) {
                      final row = rows[index];
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 18,
                          vertical: 15,
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                row.name,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 15,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                row.status,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: row.status == "Overdue"
                                      ? Colors.red
                                      : row.status == "Done"
                                      ? Colors.green
                                      : Colors.blue,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(
                                row.dueDate,
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  )
                : Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 30),
                      child: const Text(
                        "Nothing to show... yet!",
                        style: TextStyle(color: Colors.grey, fontSize: 15),
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}

class TaskRow {
  final String name;
  final String status;
  final String dueDate;

  TaskRow({required this.name, required this.status, required this.dueDate});
}
