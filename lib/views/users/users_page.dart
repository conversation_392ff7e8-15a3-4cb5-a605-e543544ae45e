// ignore_for_file: deprecated_member_use

import 'dart:io';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:excel/excel.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:legacy_pms/common/common_searchbar.dart';
import 'package:legacy_pms/common/custom_header_button.dart';
import 'package:legacy_pms/models/usermodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:legacy_pms/shared/router.dart';
import 'package:legacy_pms/views/users/add_user.dart';
import 'package:path_provider/path_provider.dart';
import '../../controller/homectrl.dart';

class UsersPage extends StatefulWidget {
  const UsersPage({super.key});

  @override
  State<UsersPage> createState() => _UsersPageState();
}

class _UsersPageState extends State<UsersPage> {
  SearchController searchctrl = SearchController();
  bool usersExporting = false;

  Future<void> sendWelcomeEmail({
    required String email,
    required String name,
    required String password,
  }) async {
    final HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'sendWelcomeEmail',
    );
    try {
      final result = await callable.call(<String, dynamic>{
        'email': email,
        'name': name,
        'password': password,
      });
      print('Welcome email sent: ${result.data}');
    } catch (e) {
      print('Failed to send welcome email: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final query = searchctrl.text.toLowerCase();

        final filteredUsers = query.isEmpty
            ? ctrl.users
            : ctrl.users.where((user) {
                final nameMatch = user.name.toLowerCase().contains(query);
                final emailMatch = (user.email).toLowerCase().contains(query);
                final phoneMatch = user.phoneNo.toString().contains(query);
                final designationMatch = (user.designation ?? '')
                    .toLowerCase()
                    .contains(query);
                return nameMatch ||
                    emailMatch ||
                    phoneMatch ||
                    designationMatch;
              }).toList();

        // Sort filtered users by createdAt descending
        filteredUsers.sort(
          (a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()),
        );

        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// Add User Button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // const SizedBox(width: 1),
                  Row(
                    children: [
                      Text(
                        "Users (${filteredUsers.length})",
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: Colors.black,
                          letterSpacing: 0.8,
                        ),
                      ),
                      SizedBox(width: 20),
                      CommonSearchBar(
                        searchController: searchctrl,
                        searchOnChanged: (p1) {
                          setState(() {});
                        },
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      usersExporting == true
                          ? CircularProgressIndicator()
                          : CustomHeaderButton(
                              onPressed: () async => await exportUsersToExcel(
                                users: filteredUsers,
                                context: context,
                              ),
                              buttonName: "Export",
                            ),
                      const SizedBox(width: 10),
                      CustomHeaderButton(
                        onPressed: () =>
                            ctrl.userRoles.contains(Permissions.canAddUser)
                            ? showDialog(
                                context: context,
                                builder: (context) => const AddUserDialog(),
                              )
                            : () {},
                        buttonName: "Add User",
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 24),

              /// Table Header
              Container(
                decoration: BoxDecoration(
                  color: logoTealColor.withOpacity(0.85),
                  borderRadius: BorderRadius.circular(6),
                ),
                padding: const EdgeInsets.symmetric(
                  vertical: 14,
                  horizontal: 20,
                ),
                child: Row(
                  children: [
                    SizedBox(
                      width: 100,
                      child: Text(
                        "Sr. No",
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: Colors.white,
                          letterSpacing: 0.8,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(flex: 2, child: _headerCell("Name", textTheme)),
                    const SizedBox(width: 16),
                    Expanded(flex: 2, child: _headerCell("Email", textTheme)),
                    const SizedBox(width: 16),
                    Expanded(flex: 2, child: _headerCell("Phone", textTheme)),
                    const SizedBox(width: 16),
                    Expanded(
                      flex: 2,
                      child: _headerCell("Designation", textTheme),
                    ),
                    const SizedBox(width: 16),
                    Expanded(flex: 2, child: _headerCell("Role", textTheme)),
                    const SizedBox(width: 16),
                    SizedBox(
                      width: 140,
                      child: Center(
                        child: Text(
                          "Actions",
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                            letterSpacing: 0.8,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 8),

              /// Table Body
              filteredUsers.isEmpty
                  ? Center(
                      heightFactor: 8,
                      child: Text(
                        "No Users Available",
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )
                  : Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                        side: BorderSide(color: logoTealColor.withOpacity(0.2)),
                      ),
                      child: Column(
                        children: List.generate(filteredUsers.length, (index) {
                          final user = filteredUsers[index];
                          final isEven = index % 2 == 0;

                          return Column(
                            children: [
                              InkWell(
                                onTap:
                                    ctrl.userRoles.contains(
                                      Permissions.canSeeUsersDetails,
                                    )
                                    ? () {
                                        context.push(
                                          '${Routes.userdetails}/${user.docId}',
                                        );
                                      }
                                    : null,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: isEven
                                        ? Colors.grey.shade50
                                        : Colors.white,
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 10,
                                    horizontal: 20,
                                  ),
                                  child: Row(
                                    children: [
                                      SizedBox(
                                        width: 100,
                                        child: Text(
                                          "${index + 1}",
                                          style: textTheme.bodyMedium?.copyWith(
                                            fontSize: 14,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        flex: 2,
                                        child: _bodyCell(
                                          user.name.capitalize.toString(),
                                          textTheme,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        flex: 2,
                                        child: _bodyCell(
                                          user.email ?? '',
                                          textTheme,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        flex: 2,
                                        child: _bodyCell(
                                          user.phoneNo.toString(),
                                          textTheme,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        flex: 2,
                                        child: _bodyCell(
                                          user.designation.toString() ?? '-',
                                          textTheme,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        flex: 2,
                                        child: _bodyCell(
                                          user.role.toString(),
                                          textTheme,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      ActionButtons(
                                        user: user,
                                        ctrl: ctrl,
                                        sendWelcomeEmail: sendWelcomeEmail,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Divider(
                                height: 1,
                                thickness: 0.7,
                                color: logoTealColor.withOpacity(0.2),
                              ),
                            ],
                          );
                        }),
                      ),
                    ),
            ],
          ),
        );
      },
    );
  }

  Future<void> exportUsersToExcel({
    required List<Usermodel> users,
    required BuildContext context,
  }) async {
    try {
      setState(() {
        usersExporting = true;
      });
      final excel = Excel.createExcel();
      final sheet = excel['Sheet1'];

      final headers = [
        'Sr.No',
        'Name',
        'Email',
        'Phone',
        'Role',
        'Status',
        'Created At',
      ];

      sheet.appendRow(headers.map((h) => TextCellValue(h)).toList());

      for (var user in users) {
        final row = [
          TextCellValue((users.indexOf(user) + 1).toString()),
          TextCellValue(user.name),
          TextCellValue(user.email ?? ''),
          TextCellValue(user.phoneNo.toString()),
          TextCellValue(user.role ?? ''),
          TextCellValue(user.status ?? ''),
          TextCellValue(
            user.createdAt != null
                ? DateFormat('dd-MM-yyyy').format(user.createdAt)
                : '',
          ),
        ];
        sheet.appendRow(row);
      }

      final excelBytes = excel.encode();

      final filename = 'Users_${DateTime.now().millisecondsSinceEpoch}.xlsx';

      if (kIsWeb) {
        await FileSaver.instance.saveFile(
          name: filename,
          bytes: Uint8List.fromList(excelBytes!),
          fileExtension: 'xlsx',
        );

        setState(() {
          usersExporting = false;
        });
      } else {
        final dir = await getExternalStorageDirectory();
        final filePath = '${dir!.path}/$filename';
        final file = File(filePath);
        await file.writeAsBytes(excelBytes!);
        showCtcAppSnackBar(context, 'Exported users to $filePath');
        setState(() {
          usersExporting = false;
        });
      }
    } catch (e) {
      setState(() {
        usersExporting = false;
      });
      debugPrint(e.toString());
      showCtcAppSnackBar(context, 'Failed to export users: $e');
      // ScaffoldMessenger.of(
      //   context,
      // ).showSnackBar(SnackBar(content: Text('Failed to export users: $e')));
    }
  }

  /// --- Helper widgets ---
  Widget _headerCell(String text, TextTheme theme, {int flex = 1}) {
    return Text(
      text,
      style: theme.titleMedium?.copyWith(
        fontWeight: FontWeight.w700,
        color: Colors.white,
        letterSpacing: 0.8,
      ),
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _bodyCell(String text, TextTheme theme, {Color? textColor}) {
    return Text(
      text,
      style: theme.bodyMedium?.copyWith(
        fontSize: 14,
        color: textColor ?? Colors.black87,
      ),
      overflow: TextOverflow.ellipsis,
    );
  }
}

class ActionButtons extends StatelessWidget {
  const ActionButtons({
    super.key,
    required this.user,
    required this.ctrl,
    required this.sendWelcomeEmail,
  });

  final Usermodel user;
  final HomeCtrl ctrl;
  final Future<void> Function({
    required String email,
    required String name,
    required String password,
  })
  sendWelcomeEmail;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 140,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Transform.scale(
            scale: 0.8,
            child: CupertinoSwitch(
              inactiveTrackColor: Colors.grey.shade400,
              value: user.status == UserStatus.active,
              onChanged: ctrl.userRoles.contains(Permissions.canEditUser)
                  ? (value) {
                      final newStatus = value
                          ? UserStatus.active
                          : UserStatus.inactive;
                      ctrl.updateUserStatus(user.docId, newStatus);
                    }
                  : null,
            ),
          ),
          if (ctrl.userRoles.contains(Permissions.canDeleteUser))
            IconButton(
              icon: const Icon(Icons.delete),
              color: Colors.redAccent,
              tooltip: "Delete User",
              onPressed: () async {
                final confirmed = await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    backgroundColor: popupBgColor,
                    title: const Text("Confirm Delete"),
                    content: const Text(
                      "Are you sure you want to delete this user?",
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text("Cancel"),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text("Delete"),
                      ),
                    ],
                  ),
                );

                if (confirmed == true) {
                  print("Deleting user");
                  await ctrl.deleteUser(user.docId);
                  print("User deleted");
                  showCtcAppSnackBar(context, 'User deleted successfully');
                }
              },
            ),
          /*    IconButton(
            onPressed: () async {
              await sendWelcomeEmail(
                email: user.email ?? '',
                name: user.name,
                password: user.password ?? '',
              );
              showCtcAppSnackBar(context, 'Send welcome email');
            },
            icon: Icon(Icons.send),
          ), */
        ],
      ),
    );
  }
}
