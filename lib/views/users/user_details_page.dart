import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:legacy_pms/models/masterqcmodel.dart';
import 'package:legacy_pms/models/taskmodel.dart';
import 'package:legacy_pms/models/usermodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/firebase.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:legacy_pms/views/settings/tasks_history_page.dart';
import 'package:legacy_pms/views/users/widgets/user_attendance_page.dart';
import 'package:pluto_grid/pluto_grid.dart';

import '../../controller/homectrl.dart';

class UserDetailsPage extends StatefulWidget {
  final String? userdocId;

  const UserDetailsPage({super.key, this.userdocId});

  @override
  State<UserDetailsPage> createState() => _UserDetailsPageState();
}

class _UserDetailsPageState extends State<UserDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController tabCtrl;
  final HomeCtrl ctrl = Get.find();
  Usermodel? user;
  bool isLoading = true;
  bool isEditing = false;
  bool isSaving = false;

  late TextEditingController nameController;
  late TextEditingController emailController;
  late TextEditingController phoneController;
  late TextEditingController passwordController;
  late TextEditingController designationController;
  String? selectedRole;
  String? selectedStatus;

  List<String> roles = [];

  List<TaskModel> userCompletedTasks = [];
  bool tasksLoading = true;
  late PlutoGridStateManager gridStateManager;

  @override
  void initState() {
    super.initState();
    tabCtrl = TabController(length: 2, vsync: this);
    initializeCtrl();
    loadUserData();
    fetchRoles();
  }

  Future<void> fetchRoles() async {
    try {
      final snapshot = await FBFireStore.roles.get();
      final fetchedRoles = snapshot.docs
          .map((doc) => (doc.data())['title'] as String)
          .toList();
      setState(() {
        roles = fetchedRoles;
      });
    } catch (e) {
      debugPrint("Error fetching roles: $e");
    }
  }

  Future<void> loadUserCompletedTasks() async {
    if (user == null) {
      setState(() {
        userCompletedTasks = [];
        tasksLoading = false;
      });
      return;
    }
    setState(() => tasksLoading = true);

    try {
      final snapshotEmployee = await FBFireStore.tasks
          .where('completed', isEqualTo: true)
          .where('employee', isEqualTo: user!.docId)
          .get();

      final snapshotSupervisor = await FBFireStore.tasks
          .where('completed', isEqualTo: true)
          .where('supervisor', isEqualTo: user!.docId)
          .get();

      final allDocs = [...snapshotEmployee.docs, ...snapshotSupervisor.docs];

      // Remove duplicates by task docId if any
      final Map<String, TaskModel> tasksMap = {};
      for (var doc in allDocs) {
        tasksMap[doc.id] = TaskModel.fromSnap(doc);
      }

      setState(() {
        userCompletedTasks = tasksMap.values.toList()
          ..sort(
            (a, b) =>
                b.completedAt?.compareTo(a.completedAt ?? DateTime(0)) ?? 0,
          );
        tasksLoading = false;
      });
    } catch (e) {
      setState(() => tasksLoading = false);
      showSnackBar('Failed to load user tasks: $e');
    }
  }

  List<PlutoColumn> _buildTaskColumns() {
    return [
      PlutoColumn(
        title: "Sr.No",
        field: "srNo",
        type: PlutoColumnType.number(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Completed At",
        field: "completedAt",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Customer",
        field: "customer",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Activity Type",
        field: "pType",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Tasks",
        field: "details",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Employee",
        field: "employee",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Supervisor",
        field: "supervisor",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Status",
        field: "status",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Result",
        field: "result",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Hours",
        field: "hours",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Report Sent",
        field: "reportSentStatus",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Customer Call",
        field: "customerCallStatus",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Call Summary",
        field: "callSummary",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "QC File",
        field: "qcFile",
        type: PlutoColumnType.text(),
        readOnly: true,
        renderer: (rendererContext) {
          final task = rendererContext.row.cells['actions']!.value as TaskModel;
          final qcId = task.qcFile;
          final qcTitle = getQcTitle(ctrl.masterQcs, qcId) ?? 'N/A';

          return InkWell(
            onTap: () async {
              final qc = ctrl.masterQcs.firstWhereOrNull(
                (m) => m.docId == qcId,
              );
              if (qc != null) {
                await showDialog(
                  context: context,
                  builder: (context) =>
                      QcHistoryViewDialog(ctrl: ctrl, masterQc: qc, task: task),
                );
              } else {
                showSnackBar('QC data not found');
              }
            },
            child: Text(
              qcTitle,
              style: TextStyle(
                color: qcId != null && qcId.isNotEmpty
                    ? Colors.blue
                    : Colors.grey,
                decoration: qcId != null && qcId.isNotEmpty
                    ? TextDecoration.underline
                    : TextDecoration.none,
              ),
            ),
          );
        },
      ),
      PlutoColumn(
        title: "Errors",
        field: "numOfErrors",
        type: PlutoColumnType.number(),
        readOnly: true,
        renderer: (rendererContext) {
          final val = rendererContext.cell.value;
          final numberVal = val is int
              ? val
              : int.tryParse(val.toString()) ?? 0;
          final color = numberVal > 0 ? Colors.red : Colors.black;
          return Text(
            val.toString(),
            style: TextStyle(
              color: color,
              fontWeight: numberVal > 0 ? FontWeight.bold : FontWeight.normal,
            ),
          );
        },
      ),
    ];
  }

  String? getQcTitle(List<Masterqcmodel> qcs, String? id) {
    if (id == null) return null;
    final qc = qcs.firstWhereOrNull((item) => item.docId == id);
    return qc?.title;
  }

  List<PlutoRow> _buildTaskRows() {
    // Use the HomeCtrl instance to resolve customer, employee, supervisor and activity names
    final ctrl = Get.find<HomeCtrl>();

    return userCompletedTasks.asMap().entries.map((entry) {
      final index = entry.key;
      final task = entry.value;

      final customer = ctrl.customers.firstWhereOrNull(
        (c) => c.docId == task.customer,
      );
      final employee = ctrl.users.firstWhereOrNull(
        (u) => u.docId == task.employee,
      );
      final supervisor = ctrl.users.firstWhereOrNull(
        (u) => u.docId == task.supervisor,
      );
      final activity = ctrl.masterTasks.firstWhereOrNull(
        (a) => a.docId == task.pType,
      );

      return PlutoRow(
        cells: {
          'srNo': PlutoCell(value: index + 1),
          'completedAt': PlutoCell(
            value: task.completedAt != null
                ? DateFormat('dd-MM-yyyy HH:mm').format(task.completedAt!)
                : '',
          ),
          'customer': PlutoCell(value: customer?.name ?? ''),
          'pType': PlutoCell(value: activity?.title ?? ''),
          'details': PlutoCell(value: task.details ?? ''),
          'employee': PlutoCell(value: employee?.name ?? ''),
          'supervisor': PlutoCell(value: supervisor?.name ?? ''),
          'status': PlutoCell(value: task.status ?? ''),
          'result': PlutoCell(value: task.result ?? ''),
          'hours': PlutoCell(value: task.supportHour ?? ''),
          'reportSentStatus': PlutoCell(value: task.reportSentStatus ?? ''),
          'customerCallStatus': PlutoCell(value: task.customerCallStatus ?? ''),
          'callSummary': PlutoCell(value: task.callSummary ?? ''),
          'qcFile': PlutoCell(
            value: getQcTitle(ctrl.masterQcs, task.qcFile) ?? 'N/A',
          ),
          'numOfErrors': PlutoCell(
            value: int.tryParse(task.numOfErrors ?? '0') ?? 0,
          ),
          'actions': PlutoCell(value: task),
        },
      );
    }).toList();
  }

  void initializeCtrl() {
    nameController = TextEditingController();
    emailController = TextEditingController();
    phoneController = TextEditingController();
    passwordController = TextEditingController();
    designationController = TextEditingController();
  }

  Future<void> loadUserData() async {
    if (widget.userdocId == null) {
      setState(() {
        isLoading = false;
      });
      return;
    }
    try {
      final userDoc = await FBFireStore.users.doc(widget.userdocId!).get();
      if (userDoc.exists) {
        setState(() {
          user = Usermodel.fromSnap(userDoc);
          _populateControllers();
          isLoading = false;
        });
        await loadUserCompletedTasks();
      } else {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      showSnackBar('Failed to load user data: $e');
    }
  }

  void _populateControllers() {
    if (user != null) {
      nameController.text = user!.name;
      emailController.text = user!.email;
      phoneController.text = user!.phoneNo.toString();
      passwordController.text = user!.password;
      designationController.text = user!.designation ?? '';
      selectedRole = user!.role;
      selectedStatus = user!.status;
    }
  }

  // void _toggleEdit() {
  //   setState(() {
  //     isEditing = !isEditing;
  //   });
  // }

  // Future<void> _saveChanges() async {
  //   if (user == null) return;
  //   setState(() {
  //     isSaving = true;
  //   });
  //   try {
  //     final updatedData = {
  //       'name': nameController.text.trim(),
  //       'email': emailController.text.trim(),
  //       'phoneNo': phoneController.text.trim(),
  //       'password': passwordController.text.trim(),
  //       'role': selectedRole,
  //       'status': selectedStatus,
  //     };
  //     await FBFireStore.users.doc(user!.docId).update(updatedData);
  //     setState(() {
  //       isEditing = false;
  //       isSaving = false;
  //     });
  //     showSnackBar('User updated successfully');
  //     loadUserData();
  //   } catch (e) {
  //     setState(() {
  //       isSaving = false;
  //     });
  //     showSnackBar('Failed to update user: $e');
  //   }
  // }

  @override
  void dispose() {
    tabCtrl.dispose();
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    passwordController.dispose();
    designationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: isLoading
              ? const Center(child: CircularProgressIndicator())
              : user == null
              ? _buildUserNotFound()
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // _buildHeader(),
                    // const SizedBox(height: 10),
                    _buildTabBar(),
                    const SizedBox(height: 12),
                    Expanded(
                      child: TabBarView(
                        physics: NeverScrollableScrollPhysics(),
                        controller: tabCtrl,
                        children: [
                          // Insert the card before the attendance page
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // const SizedBox(height: 16),
                              Expanded(
                                child: UserAttendancePage(userId: user!.docId),
                              ),
                            ],
                          ),
                          _buildTasksTab(),
                        ],
                      ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  // Card widget to show total attendance for the user from ctrl

  Widget _buildUserNotFound() {
    return Center(
      child: Card(
        elevation: 3,
        margin: const EdgeInsets.symmetric(horizontal: 40),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(28),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.person_off, size: 64, color: Colors.grey),
              const SizedBox(height: 24),
              Text(
                'User not found',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.grey[800],
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.pop(),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 28,
                    vertical: 14,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Go Back', style: TextStyle(fontSize: 16)),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: TabBar(
              // isScrollable: false,
              dividerHeight: 0,
              dividerColor: Colors.transparent,
              controller: tabCtrl,
              indicatorColor: logoTealColor,
              labelColor: logoTealColor,
              unselectedLabelColor: Colors.grey.shade600,
              // indicatorWeight: 1,
              labelStyle: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              tabs: const [
                Tab(text: 'Details'),
                Tab(text: 'Tasks'),
              ],
            ),
          ),
          // const SizedBox(width: 12),
          // AnimatedSwitcher(
          //   duration: const Duration(milliseconds: 250),
          //   transitionBuilder: (child, animation) =>
          //       ScaleTransition(scale: animation, child: child),
          //   child: !isEditing
          //       ? IconButton(
          //           key: const ValueKey('edit'),
          //           onPressed: ctrl.userRoles.contains(Permissions.canEditUser)
          //               ? () => _toggleEdit()
          //               : () {},
          //           // _toggleEdit,
          //           icon: Icon(Icons.edit, color: Colors.teal[700]),
          //           tooltip: 'Edit User',
          //           splashRadius: 26,
          //         )
          //       : Row(
          //           key: const ValueKey('edit_actions'),
          //           mainAxisSize: MainAxisSize.min,
          //           children: [
          //             if (isSaving)
          //               const SizedBox(
          //                 width: 26,
          //                 height: 26,
          //                 child: CircularProgressIndicator(
          //                   strokeWidth: 2,
          //                   valueColor: AlwaysStoppedAnimation<Color>(
          //                     Colors.teal,
          //                   ),
          //                 ),
          //               )
          //             else
          //               IconButton(
          //                 onPressed: _saveChanges,
          //                 icon: Icon(Icons.save, color: Colors.teal[700]),
          //                 tooltip: 'Save Changes',
          //                 splashRadius: 26,
          //               ),
          //             IconButton(
          //               onPressed: _toggleEdit,
          //               icon: Icon(Icons.close, color: Colors.grey[600]),
          //               tooltip: 'Cancel Edit',
          //               splashRadius: 26,
          //             ),
          //           ],
          //         ),
          // ),
        ],
      ),
    );
  }

  Widget _buildTasksTab() {
    if (tasksLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (userCompletedTasks.isEmpty) {
      return Center(
        child: Text(
          'No completed tasks found for this user.',
          style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
        ),
      );
    }
    return Padding(
      padding: const EdgeInsets.all(20),
      child: PlutoGrid(
        columns: _buildTaskColumns(),
        rows: _buildTaskRows(),
        onLoaded: (event) {
          gridStateManager = event.stateManager;
        },
        configuration: PlutoGridConfiguration(
          style: PlutoGridStyleConfig(
            borderColor: Colors.grey.shade300,
            rowHeight: 45,
            gridBorderColor: Colors.grey.shade200,
          ),
        ),
      ),
    );
  }
}

// Widget _buildDetailsTab() {
//   return SingleChildScrollView(
//     padding: const EdgeInsets.all(20),
//     child: Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         _detailRow("Name", nameController, isEditing: isEditing),
//         _divider(),
//         _detailRow(
//           "Email",
//           emailController,
//           isEditing: false,
//         ), // Always readonly
//         _divider(),
//         _detailRow("Phone", phoneController, isEditing: isEditing),
//         _divider(),
//         _dropdownRow(
//           "Role",
//           selectedRole,
//           roles,
//           (val) => setState(() => selectedRole = val),
//         ),
//         _divider(),
//         _dropdownRow("Status", selectedStatus, [
//           'active',
//           'inactive',
//           'pending',
//         ], (val) => setState(() => selectedStatus = val)),
//       ],
//     ),
//   );
// }

// Widget _divider() => Divider(color: Colors.grey.shade300, height: 32);

// Widget _detailRow(
//   String label,
//   TextEditingController controller, {
//   bool isEditing = false,
// }) {
//   return Row(
//     crossAxisAlignment: CrossAxisAlignment.center,
//     children: [
//       SizedBox(
//         width: 120,
//         child: Text(
//           label,
//           style: const TextStyle(
//             fontWeight: FontWeight.w600,
//             color: Colors.grey,
//           ),
//         ),
//       ),
//       isEditing
//           ? SizedBox(
//               width: 300,
//               child: TextField(
//                 controller: controller,
//                 decoration: InputDecoration(
//                   hintText: "Enter $label",
//                   isDense: true,
//                   contentPadding: const EdgeInsets.symmetric(
//                     horizontal: 12,
//                     vertical: 10,
//                   ),
//                   border: OutlineInputBorder(
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                   focusedBorder: OutlineInputBorder(
//                     borderSide: BorderSide(color: Colors.teal, width: 2),
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                 ),
//               ),
//             )
//           : Text(
//               controller.text.isNotEmpty ? controller.text : "Not specified",
//               style: const TextStyle(fontSize: 16, color: Colors.black87),
//             ),
//     ],
//   );
// }

// Widget _dropdownRow(
//   String label,
//   String? value,
//   List<String> options,
//   ValueChanged<String?> onChanged,
// ) {
//   return Row(
//     crossAxisAlignment: CrossAxisAlignment.center,
//     children: [
//       SizedBox(
//         width: 120,
//         child: Text(
//           label,
//           style: const TextStyle(
//             fontWeight: FontWeight.w600,
//             color: Colors.grey,
//           ),
//         ),
//       ),
//       isEditing
//           ? SizedBox(
//               width: 300,
//               child: DropdownButtonFormField<String>(
//                 value: value,
//                 items: options
//                     .map(
//                       (option) =>
//                           DropdownMenuItem(value: option, child: Text(option)),
//                     )
//                     .toList(),
//                 onChanged: onChanged,
//                 decoration: InputDecoration(
//                   isDense: true,
//                   contentPadding: const EdgeInsets.symmetric(
//                     horizontal: 12,
//                     vertical: 10,
//                   ),
//                   border: OutlineInputBorder(
//                     borderRadius: BorderRadius.circular(8),
//                   ),
//                 ),
//               ),
//             )
//           : Text(
//               value ?? "Not specified",
//               style: const TextStyle(fontSize: 16, color: Colors.black87),
//             ),
//     ],
//   );
// }
