import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:legacy_pms/models/punchmodel.dart';
import 'package:legacy_pms/models/recordsmodel.dart';
import 'package:legacy_pms/shared/firebase.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:legacy_pms/views/users/widgets/leave_widgets.dart';
import 'package:legacy_pms/views/users/widgets/ui_components.dart';
import 'dart:async';

enum ViewType { month, customDate }

class UserAttendancePage extends StatefulWidget {
  final String userId;
  const UserAttendancePage({super.key, required this.userId});

  @override
  State<UserAttendancePage> createState() => _UserAttendancePageState();
}

class _UserAttendancePageState extends State<UserAttendancePage> {
  ViewType _selectedViewType = ViewType.customDate;
  int? _selectedMonth;
  DateTime _selectedDate = DateTime.now();

  late final ScrollController _scrollController;

  List<Recordsmodel> _allRecords = [];
  List<Recordsmodel> _filteredRecords = [];
  List<Punchmodel> _selectedDatePunches = [];

  bool _isLoadingRecords = true;
  bool _isLoadingPunches = false;
  String? _errorMessage;

  // Stream subscription for current date punches
  StreamSubscription<List<Punchmodel>>? _punchesStreamSubscription;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _fetchRecords();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _punchesStreamSubscription?.cancel();
    super.dispose();
  }

  // Check if the selected date is today
  bool _isSelectedDateToday() {
    final now = DateTime.now();
    return _selectedDate.year == now.year &&
        _selectedDate.month == now.month &&
        _selectedDate.day == now.day;
  }

  Future<void> _fetchRecords() async {
    setState(() {
      _isLoadingRecords = true;
      _errorMessage = null;
    });
    try {
      final currentYear = DateTime.now().year;
      final snapshot = await FBFireStore.records
          .where('uId', isEqualTo: widget.userId)
          .where(
            'createdAt',
            isGreaterThanOrEqualTo: DateTime(currentYear, 1, 1),
          )
          .where(
            'createdAt',
            isLessThanOrEqualTo: DateTime(currentYear, 12, 31, 23, 59, 59),
          )
          .get();

      final records = snapshot.docs
          .map((doc) => Recordsmodel.fromSnap(doc))
          .toList();

      setState(() {
        _allRecords = records;
        _isLoadingRecords = false;
        _errorMessage = null;
      });
      _updateFilteredRecords();
      if (_selectedViewType == ViewType.customDate) {
        _loadPunchesForDate(_selectedDate);
      }
    } catch (e) {
      setState(() {
        _isLoadingRecords = false;
        _errorMessage = "Error loading attendance records";
      });
    }
  }

  // Stream method for current date punches
  Stream<List<Punchmodel>> _getCurrentDatePunchesStream() {
    final now = DateTime.now();
    final startOfDay = DateTime(now.year, now.month, now.day);
    final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);

    return FBFireStore.punches
        .where('uId', isEqualTo: widget.userId)
        .where('createdAt', isGreaterThanOrEqualTo: startOfDay)
        .where('createdAt', isLessThanOrEqualTo: endOfDay)
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Punchmodel.fromSnap(doc)).toList(),
        );
  }

  Future<void> _loadPunchesForDate(DateTime date) async {
    setState(() => _isLoadingPunches = true);

    // Cancel existing stream subscription first
    _punchesStreamSubscription?.cancel();
    _punchesStreamSubscription = null;

    if (_isSelectedDateToday()) {
      // Use stream for current date
      _punchesStreamSubscription = _getCurrentDatePunchesStream().listen(
        (punches) {
          if (mounted) {
            setState(() {
              _selectedDatePunches = punches;
              _isLoadingPunches = false;
            });
          }
        },
        onError: (error) {
          if (mounted) {
            setState(() {
              _selectedDatePunches = [];
              _isLoadingPunches = false;
            });
          }
        },
      );
    } else {
      // Use get method for other dates
      try {
        final punches = await fetchUserPunchesForDate(date, widget.userId);
        // Sort punches by creation time
        punches.sort((a, b) => a.createdAt.compareTo(b.createdAt));

        if (mounted) {
          setState(() {
            _selectedDatePunches = punches;
            _isLoadingPunches = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _selectedDatePunches = [];
            _isLoadingPunches = false;
          });
        }
      }
    }
  }

  void _updateFilteredRecords() {
    List<Recordsmodel> filtered = [];
    if (_selectedViewType == ViewType.month && _selectedMonth != null) {
      final currentYear = DateTime.now().year;
      filtered = _allRecords
          .where(
            (record) =>
                record.createdAt.year == currentYear &&
                record.createdAt.month == _selectedMonth!,
          )
          .toList();
    } else if (_selectedViewType == ViewType.customDate) {
      filtered = _allRecords
          .where(
            (record) =>
                record.createdAt.year == _selectedDate.year &&
                record.createdAt.month == _selectedDate.month,
          )
          .toList();
    }
    setState(() => _filteredRecords = filtered);
  }

  void _onViewTypeChanged(ViewType newType) {
    if (_selectedViewType == newType) return;

    setState(() {
      _selectedViewType = newType;
      _selectedMonth = null;
      if (newType == ViewType.customDate) _selectedDate = DateTime.now();
    });

    _updateFilteredRecords();
    if (newType == ViewType.customDate) {
      _loadPunchesForDate(_selectedDate);
    } else {
      // Cancel stream when switching to month view
      _punchesStreamSubscription?.cancel();
      _punchesStreamSubscription = null;
      setState(() => _selectedDatePunches = []);
    }
  }

  void _onMonthChanged(int? month) {
    if (_selectedMonth == month) return;

    // Cancel stream when switching to month view
    _punchesStreamSubscription?.cancel();

    setState(() => _selectedMonth = month);
    _updateFilteredRecords();
  }

  void _onDateChanged(DateTime date) {
    if (_selectedDate.year == date.year &&
        _selectedDate.month == date.month &&
        _selectedDate.day == date.day)
      return;

    setState(() => _selectedDate = date);
    _updateFilteredRecords();
    _loadPunchesForDate(date);
  }

  Future<void> _addLeave(DateTime leaveDate) async {
    try {
      final record = _allRecords.firstWhereOrNull(
        (r) =>
            r.createdAt.year == leaveDate.year &&
            r.createdAt.month == leaveDate.month,
      );

      if (record == null) {
        showSnackBar("No record found for this month.");
        return;
      }

      final leaveDateStr = DateFormat('dd-MM-yyyy').format(leaveDate);
      final leaveDates = List<String>.from(record.leavedates ?? []);

      if (!leaveDates.contains(leaveDateStr)) {
        leaveDates.add(leaveDateStr);
        await FBFireStore.records.doc(record.docId).update({
          'leavedates': leaveDates,
          'totalLeaves': (record.totalLeaves ?? 0) + 1,
        });
        showSnackBar("Leave added successfully.");
        // Refresh records to show updated leave data
        _fetchRecords();
      } else {
        showSnackBar("Leave already exists for this date.");
      }
    } catch (e) {
      showSnackBar("Failed to add leave: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xfff5f7fa),
      child: SingleChildScrollView(
        controller: _scrollController,
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const ZPageHeader(),
              const SizedBox(height: 24),
              ZFilterBar(
                selectedViewType: _selectedViewType,
                selectedMonth: _selectedMonth,
                selectedDate: _selectedDate,
                onViewTypeChanged: _onViewTypeChanged,
                onMonthChanged: _onMonthChanged,
                onDateChanged: _onDateChanged,
                onAddLeave: () =>
                    ZLeaveDialog.show(context, _selectedDate, _addLeave),
              ),
              const SizedBox(height: 24),
              ZContentArea(
                userId: widget.userId,
                isLoading: _isLoadingRecords,
                errorMessage: _errorMessage,
                viewType: _selectedViewType,
                selectedMonth: _selectedMonth,
                filteredRecords: _filteredRecords,
                selectedDatePunches: _selectedDatePunches,
                isLoadingPunches: _isLoadingPunches,
                selectedDate: _selectedDate,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
