import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/models/recordsmodel.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:legacy_pms/views/users/widgets/user_personal_details.dart';

class DBCardRow1 extends StatefulWidget {
  const DBCardRow1({
    super.key,
    required this.todayMins,
    this.userRecord,
    required this.userId,
  });

  final int todayMins;
  final Recordsmodel? userRecord;
  final String userId;

  @override
  State<DBCardRow1> createState() => _DBCardRow1State();
}

Recordsmodel? recordmodel;

class _DBCardRow1State extends State<DBCardRow1> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cardColor = theme.cardColor;
    final primary = theme.colorScheme.primary;
    final secondary = theme.colorScheme.secondary;
    final surfaceVariant = theme.colorScheme.surfaceContainerHighest;
    final onSurface = theme.colorScheme.onSurface;
    final textTheme = theme.textTheme;

    // Use theme colors for cards, fallback to Material colors if needed
    final List<_CardData> cards = [
      // _CardData(
      //   extraText: "of this month",
      //   icon: CupertinoIcons.calendar,
      //   text: "Total Attendance",
      //   dataText: "${widget.userRecord?.totalAttendance ?? 0}",
      //   color: cardColor,
      //   iconColor: primary,
      // ),
      _CardData(
        extraText: "of this month",
        icon: CupertinoIcons.calendar,
        text: "Total Attendance",
        dataText: "${widget.userRecord?.totalAttendance ?? 0}",
        color: cardColor,
        iconColor: primary,
      ),
      _CardData(
        extraText: "till now",
        icon: Icons.hourglass_empty,
        text: "Todays Hours",
        dataText: formatMinutesToHourMinute(widget.todayMins),
        color: surfaceVariant,
        iconColor: secondary,
      ),
      _CardData(
        extraText: "of this month",
        icon: CupertinoIcons.home,
        text: "Total Leaves",
        dataText: "${widget.userRecord?.totalLeaves ?? 0}",
        color: cardColor,
        iconColor: theme.colorScheme.tertiary,
      ),
      _CardData(
        extraText: "of this month",
        icon: CupertinoIcons.clock,
        text: "Total Hours",
        dataText: "--",
        color: surfaceVariant,
        iconColor: primary,
      ),
    ];

    return StaggeredGrid.extent(
      crossAxisSpacing: 20,
      mainAxisSpacing: 20,
      maxCrossAxisExtent: 300,
      children: [
        UserPersonalDetails(
          user: Get.find<HomeCtrl>().users.firstWhereOrNull(
            (u) => u.docId == widget.userId,
          ),
          ctrl: Get.find<HomeCtrl>(),
        ),
        ...cards.map((card) {
          return Card(
            color: Colors.white,
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircleAvatar(
                    backgroundColor: card.iconColor.withOpacity(0.12),
                    radius: 24,
                    child: Icon(card.icon, color: card.iconColor, size: 28),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    card.text,
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: onSurface,
                      letterSpacing: 0.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    card.dataText,
                    style: textTheme.headlineSmall?.copyWith(
                      color: card.iconColor,
                      fontWeight: FontWeight.w700,
                      fontSize: 28,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    card.extraText,
                    style: textTheme.labelMedium?.copyWith(
                      color: theme.hintColor,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 1.1,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }),
      ],
    );
  }
}

class _CardData {
  final String extraText;
  final IconData icon;
  final String text;
  final String dataText;
  final Color color;
  final Color iconColor;

  _CardData({
    required this.extraText,
    required this.icon,
    required this.text,
    required this.dataText,
    required this.color,
    required this.iconColor,
  });

  _CardData copyWith({
    String? extraText,
    IconData? icon,
    String? text,
    String? dataText,
    Color? color,
    Color? iconColor,
  }) {
    return _CardData(
      extraText: extraText ?? this.extraText,
      icon: icon ?? this.icon,
      text: text ?? this.text,
      dataText: dataText ?? this.dataText,
      color: color ?? this.color,
      iconColor: iconColor ?? this.iconColor,
    );
  }
}
