import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:legacy_pms/models/punchmodel.dart';

class DBCardRow2 extends StatelessWidget {
  const DBCardRow2({super.key, required this.punches});

  final List<Punchmodel> punches;

  @override
  Widget build(BuildContext context) {
    final List<Punchmodel> todaysPunches = punches;
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Row(
      key: ValueKey(DateTime.now()),
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 500,
          child: Card(
            color: Colors.white,
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(30.0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Attendance",
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 25),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            "SR.NO.",
                            style: textTheme.labelMedium?.copyWith(
                              color: theme.hintColor,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 1.1,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            "PUNCH",
                            style: textTheme.labelMedium?.copyWith(
                              color: theme.hintColor,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 1.1,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            "TIME",
                            style: textTheme.labelMedium?.copyWith(
                              color: theme.hintColor,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 1.1,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Divider(
                    color: theme.dividerColor,
                    thickness: 1,
                    indent: 0,
                    endIndent: 0,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: todaysPunches.isEmpty
                        ? Center(
                            child: Text(
                              "No punches found",
                              style: textTheme.bodyMedium?.copyWith(
                                color: theme.hintColor,
                              ),
                            ),
                          )
                        : ListView.builder(
                            itemCount: todaysPunches.length,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              final punch = todaysPunches[index];
                              final isIn = punch.punchIn;
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 10),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 8.0,
                                          ),
                                          child: Text(
                                            (index + 1).toString(),
                                            style: textTheme.bodyMedium
                                                ?.copyWith(
                                                  color: theme
                                                      .colorScheme
                                                      .onSurface,
                                                ),
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 8.0,
                                          ),
                                          child: Text(
                                            isIn ? "IN" : "OUT",
                                            style: textTheme.bodyMedium
                                                ?.copyWith(
                                                  fontWeight: FontWeight.w700,
                                                ),
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 8.0,
                                          ),
                                          child: Text(
                                            DateFormat(
                                              'hh:mm aa',
                                            ).format(punch.createdAt),
                                            style: textTheme.bodyMedium
                                                ?.copyWith(
                                                  color: theme
                                                      .colorScheme
                                                      .onSurface,
                                                ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 30),
        const SizedBox(width: 20),
      ],
    );
  }
}
