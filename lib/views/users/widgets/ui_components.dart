// Page Header Component
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:legacy_pms/models/punchmodel.dart';
import 'package:legacy_pms/models/recordsmodel.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:legacy_pms/views/users/widgets/dbcard_row1.dart';
import 'package:legacy_pms/views/users/widgets/dbcard_row2.dart';
import 'package:legacy_pms/views/users/widgets/leave_widgets.dart';
import 'package:legacy_pms/views/users/widgets/user_attendance_page.dart';

class ZPageHeader extends StatelessWidget {
  const ZPageHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Icon(
          Icons.calendar_month_rounded,
          color: Color(0xff1976d2),
          size: 28,
        ),
        const SizedBox(width: 12),
        Text(
          "Attendance Overview",
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.blueGrey.shade900,
            letterSpacing: 0.5,
          ),
        ),
      ],
    );
  }
}

// Filter Bar Component
class ZFilterBar extends StatelessWidget {
  final ViewType selectedViewType;
  final int? selectedMonth;
  final DateTime selectedDate;
  final ValueChanged<ViewType> onViewTypeChanged;
  final ValueChanged<int?> onMonthChanged;
  final ValueChanged<DateTime> onDateChanged;
  final VoidCallback onAddLeave;

  const ZFilterBar({
    super.key,
    required this.selectedViewType,
    required this.selectedMonth,
    required this.selectedDate,
    required this.onViewTypeChanged,
    required this.onMonthChanged,
    required this.onDateChanged,
    required this.onAddLeave,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(18),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            blurRadius: 16,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(Icons.filter_alt_rounded, color: Color(0xff5b5b5b)),
          const SizedBox(width: 16),
          ZViewToggle(
            selectedViewType: selectedViewType,
            onChanged: onViewTypeChanged,
          ),
          const SizedBox(width: 24),
          if (selectedViewType == ViewType.month)
            ZMonthSelector(
              selectedMonth: selectedMonth,
              onChanged: onMonthChanged,
            ),
          if (selectedViewType == ViewType.customDate) ...[
            ZDateSelector(selectedDate: selectedDate, onChanged: onDateChanged),
          ],
          const Spacer(),
          ZAddLeaveButton(onPressed: onAddLeave),
        ],
      ),
    );
  }
}

// View Toggle Component
class ZViewToggle extends StatelessWidget {
  final ViewType selectedViewType;
  final ValueChanged<ViewType> onChanged;

  const ZViewToggle({
    super.key,
    required this.selectedViewType,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return ToggleButtons(
      borderRadius: BorderRadius.circular(12),
      isSelected: [
        selectedViewType == ViewType.month,
        selectedViewType == ViewType.customDate,
      ],
      onPressed: (index) => onChanged(ViewType.values[index]),
      selectedColor: Colors.white,
      fillColor: const Color(0xFFEE8023),
      color: Colors.black87,
      children: const [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 18, vertical: 8),
          child: Text('Month', style: TextStyle(fontWeight: FontWeight.w600)),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 18, vertical: 8),
          child: Text(
            'Custom Date',
            style: TextStyle(fontWeight: FontWeight.w600),
          ),
        ),
      ],
    );
  }
}

// Month Selector Component
class ZMonthSelector extends StatelessWidget {
  final int? selectedMonth;
  final ValueChanged<int?> onChanged;

  const ZMonthSelector({
    super.key,
    required this.selectedMonth,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color(0xfff5f7fa),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int>(
          value: selectedMonth,
          hint: const Text("Select Month"),
          items: List.generate(
            12,
            (index) => DropdownMenuItem(
              value: index + 1,
              child: Text(DateFormat.MMMM().format(DateTime(0, index + 1))),
            ),
          ),
          onChanged: onChanged,
        ),
      ),
    );
  }
}

// Date Selector Component
class ZDateSelector extends StatelessWidget {
  final DateTime selectedDate;
  final ValueChanged<DateTime> onChanged;

  const ZDateSelector({
    super.key,
    required this.selectedDate,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        ElevatedButton.icon(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xfff5f7fa),
            foregroundColor: Colors.black87,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          ),
          onPressed: () async {
            final picked = await showDatePicker(
              context: context,
              initialDate: selectedDate,
              firstDate: DateTime(2000),
              lastDate: DateTime.now().add(const Duration(days: 365)),
            );
            if (picked != null) onChanged(picked);
          },
          icon: const Icon(Icons.calendar_today_rounded, size: 16),
          label: const Text(
            "Select Date",
            style: TextStyle(fontWeight: FontWeight.w600),
          ),
        ),
        const SizedBox(width: 12),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: const Color(0xffe3eafc),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            DateFormat('dd MMM yyyy').format(selectedDate),
            style: const TextStyle(
              color: Color(0xff2d2d2d),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}

// Add Leave Button Component

// Content Area Component
class ZContentArea extends StatelessWidget {
  final bool isLoading;
  final String? errorMessage;
  final ViewType viewType;
  final int? selectedMonth;
  final List<Recordsmodel> filteredRecords;
  final List<Punchmodel> selectedDatePunches;
  final bool isLoadingPunches;
  final DateTime selectedDate;
  final String userId;

  const ZContentArea({
    super.key,
    required this.isLoading,
    required this.errorMessage,
    required this.viewType,
    required this.selectedMonth,
    required this.filteredRecords,
    required this.selectedDatePunches,
    required this.isLoadingPunches,
    required this.selectedDate,
    required this.userId,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) return const Center(child: CircularProgressIndicator());
    if (errorMessage != null) return Center(child: Text(errorMessage!));

    if (viewType == ViewType.customDate) {
      // if (filteredRecords.isNotEmpty) {
      return ZCustomDateView(
        filteredRecords: filteredRecords,
        selectedDatePunches: selectedDatePunches,
        isLoadingPunches: isLoadingPunches,
        selectedDate: selectedDate,
        userId: userId,
      );
      // }
      return const ZEmptyState(message: "No records found for selected date");
    }

    if (selectedMonth == null) {
      return const ZEmptyState(
        message: "Please select a month to view records",
      );
    }

    if (filteredRecords.isNotEmpty) {
      return ZMonthView(records: filteredRecords);
    }

    return const ZEmptyState(message: "No records found for selected month");
  }
}

// Custom Date View Component
class ZCustomDateView extends StatelessWidget {
  final List<Recordsmodel> filteredRecords;
  final List<Punchmodel> selectedDatePunches;
  final bool isLoadingPunches;
  final DateTime selectedDate;
  final String userId;

  const ZCustomDateView({
    super.key,
    required this.filteredRecords,
    required this.userId,
    required this.selectedDatePunches,
    required this.isLoadingPunches,
    required this.selectedDate,
  });

  @override
  Widget build(BuildContext context) {
    final totalMins = filteredRecords.isNotEmpty
        ? filteredRecords.first.totalMinutes ?? 0
        : 0;
    final userRecord = filteredRecords.firstWhereOrNull(
      (u) => u.createdAt == DateTime(selectedDate.year, selectedDate.month, 1),
    );

    return Column(
      children: [
        DBCardRow1(
          todayMins: totalMins,
          userRecord: userRecord,
          userId: userId,
        ),
        const SizedBox(height: 32),
        isLoadingPunches
            ? const Center(child: CircularProgressIndicator())
            : DBCardRow2(punches: selectedDatePunches),
      ],
    );
  }
}

// Month View Component
class ZMonthView extends StatelessWidget {
  final List<Recordsmodel> records;

  const ZMonthView({super.key, required this.records});

  @override
  Widget build(BuildContext context) {
    return AlignedGridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: records.length,
      crossAxisCount: 3,
      mainAxisSpacing: 24,
      crossAxisSpacing: 24,
      itemBuilder: (context, index) => ZMonthCard(record: records[index]),
    );
  }
}

// Month Card Component
class ZMonthCard extends StatelessWidget {
  final Recordsmodel record;

  const ZMonthCard({super.key, required this.record});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: [
            const Color(0xffaac9ff).withOpacity(0.8),
            const Color(0xffe3eafc).withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blueGrey.withOpacity(0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              DateFormat('MMMM yyyy').format(record.createdAt),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xff2d2d2d),
              ),
            ),
            const SizedBox(height: 16),
            ZStatRow(
              Icons.check_circle,
              const Color(0xff4caf50),
              "Attendance: ${record.totalAttendance}",
            ),
            const SizedBox(height: 8),
            ZStatRow(
              Icons.access_time,
              const Color(0xff1976d2),
              "Hours: ${formatMinutesToHourMinute(record.totalMinutes ?? 0)}",
            ),
            const SizedBox(height: 8),
            ZStatRow(
              Icons.beach_access,
              const Color(0xfffbc02d),
              "Leaves: ${record.totalLeaves}",
            ),
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(
                  Icons.event_note,
                  color: Color(0xff7e57c2),
                  size: 18,
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Text(
                    "Dates: ${(record.leavedates?.isEmpty ?? true) ? "-" : record.leavedates?.join(", ")}",
                    style: const TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Stat Row Component
class ZStatRow extends StatelessWidget {
  final IconData icon;
  final Color color;
  final String text;

  const ZStatRow(this.icon, this.color, this.text, {super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(icon, color: color, size: 18),
        const SizedBox(width: 8),
        Text(
          text,
          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }
}

// Empty State Component
class ZEmptyState extends StatelessWidget {
  final String message;

  const ZEmptyState({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 60),
      child: Center(
        child: Column(
          children: [
            Icon(Icons.info_outline, color: Colors.grey.shade400, size: 48),
            const SizedBox(height: 12),
            Text(
              message,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// Leave Dialog Component
