// ignore_for_file: use_build_context_synchronously

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:legacy_pms/common/multiselectbuttonformfield.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/models/rolemodel.dart';
import 'package:legacy_pms/models/usermodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/firebase.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

class UserPersonalDetails extends StatefulWidget {
  final Usermodel? user;
  final HomeCtrl ctrl;

  const UserPersonalDetails({
    super.key,
    required this.user,
    required this.ctrl,
  });

  @override
  State<UserPersonalDetails> createState() => _UserPersonalDetailsState();
}

class _UserPersonalDetailsState extends State<UserPersonalDetails> {
  List<String> roles = [];

  String? selectedRole;

  bool isEditing = false;

  late TextEditingController nameController;
  late TextEditingController emailController;
  late TextEditingController phoneController;
  late TextEditingController passwordController;
  late TextEditingController roleController;
  late TextEditingController statusController;
  late TextEditingController designationController;

  // For status switch
  late bool statusSwitchValue;

  void loadRoles() {
    final ctrlRoles = widget.ctrl.roles.map((r) => r.title).toList();
    print('Loaded roles for dropdown: $ctrlRoles');
    setState(() {
      roles = ctrlRoles;
    });
  }

  @override
  void initState() {
    super.initState();

    selectedRole = widget.user?.role;
    loadRoles();
    nameController = TextEditingController(text: widget.user?.name ?? '');
    emailController = TextEditingController(text: widget.user?.email ?? '');
    phoneController = TextEditingController(text: widget.user?.phoneNo ?? '');
    passwordController = TextEditingController(
      text: widget.user?.password ?? '',
    );
    roleController = TextEditingController(text: widget.user?.role ?? '');
    statusController = TextEditingController(text: widget.user?.status ?? '');
    designationController = TextEditingController(
      text: widget.user?.designation ?? '',
    );

    // Assume status is "active" or "inactive" (case-insensitive)
    statusSwitchValue =
        (widget.user?.status?.toLowerCase() == UserStatus.active);
  }

  @override
  void dispose() {
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    passwordController.dispose();
    roleController.dispose();
    statusController.dispose();
    designationController.dispose();
    super.dispose();
  }

  Future<void> _saveDetailsToFirebase({
    required String name,
    required String email,
    required String phone,
    required String password,
    required String role,
    required String status,
    required List<String> customers,
    required String? designation,
  }) async {
    final user = widget.user;
    if (user == null) return;

    final roleQuery = await FBFireStore.roles
        .where('title', isEqualTo: role)
        .limit(1)
        .get();

    List<String> newRolePermissions = [];
    if (roleQuery.docs.isNotEmpty) {
      final roleDoc = roleQuery.docs.first;
      final roleModel = Rolemodel.fromSnap(roleDoc);
      newRolePermissions = roleModel.permissions;
      print('Fetched permissions for role "$role": $newRolePermissions');
    }

    final bool emailChanged = user.email != email.trim();
    bool emailUpdateSuccess = true;

    // Try to update Auth email first if changed
    if (emailChanged) {
      emailUpdateSuccess = await _updateUserEmailInAuth(
        user.docId,
        email.trim(),
        user.email,
      );
      if (!emailUpdateSuccess) {
        // If updating email in Auth/Firestore fails, abort the entire update!
        return;
      }
    }

    final updatedUser = user.copyWith(
      name: name,
      email: email,
      phoneNo: phone,
      password: password,
      role: role,
      status: status,
      customers: customers,
      designation: designation,
    );

    // Update local state
    setState(() {
      nameController.text = name;
      emailController.text = email;
      phoneController.text = phone;
      passwordController.text = password;
      roleController.text = role;
      statusController.text = status;
      statusSwitchValue = status.toLowerCase() == UserStatus.active;
      designationController.text = designation ?? '';
    });

    // Save to Firestore (additional update is safe when Auth/Firestore already changed email, due to merge)
    await FBFireStore.users
        .doc(user.docId)
        .set(updatedUser.toJson(), SetOptions(merge: true));

    print('User details updated in Firestore.');

    showCtcAppSnackBar(context, 'User details updated!');
  }

  // void _toggleEdit() {
  //   setState(() {
  //     isEditing = !isEditing;
  //     // If entering edit mode, sync switch with controller
  //     if (isEditing) {
  //       statusSwitchValue = (statusController.text.toLowerCase() == 'active');
  //     }
  //   });
  // }

  // void _saveDetails() {
  //   // Here you would update the user in your controller or backend
  //   setState(() {
  //     // Update statusController text from switch value before saving
  //     statusController.text = statusSwitchValue ? 'Active' : 'Inactive';
  //     isEditing = false;
  //     // Optionally update the user in ctrl.users or call a method to persist changes
  //   });
  //   ScaffoldMessenger.of(
  //     context,
  //   ).showSnackBar(const SnackBar(content: Text('User details updated!')));
  // }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      color: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.only(bottom: 0),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 22),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row with Avatar, Title, and Edit/Save/Cancel
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Expanded(
                  child: _buildDetailRowWithIcon(
                    icon: Icons.person,
                    label: 'Name',
                    controller: nameController,
                    editing: isEditing,
                  ),
                ),
                const SizedBox(width: 12),
                // You can add a user avatar here if desired
                Get.find<HomeCtrl>().loggedInUser?.role == admin
                    ? IconButton(
                        icon: Icon(Icons.edit, color: Colors.blue[700]),
                        tooltip: 'Edit',
                        onPressed: () {
                          _showEditDialog(context);
                        },
                        // _saveDetails,
                      )
                    : SizedBox.shrink(),
                // if (isEditing)
                //   IconButton(
                //     icon: const Icon(Icons.cancel, color: Colors.red),
                //     tooltip: 'Cancel',
                //     onPressed: _toggleEdit,
                //   ),
              ],
            ),
            // Details with icons/logos

            // _buildDetailRowWithIcon(
            //   icon: Icons.email,
            //   label: 'Email',
            //   controller: emailController,
            //   editing: isEditing,
            // ),
            _buildDetailRowWithIcon(
              icon: Icons.phone,
              label: 'Phone',
              controller: phoneController,
              editing: isEditing,
            ),
            _buildDetailRowWithIcon(
              icon: Icons.work,
              label: 'Role',
              controller: roleController,
              editing: isEditing,
            ),
            _buildStatusRowWithSwitch(
              icon: Icons.verified_user,
              label: 'Status',
              controller: statusController,
              editing: isEditing,
              switchValue: statusSwitchValue,
              onSwitchChanged: (val) {
                setState(() {
                  statusSwitchValue = val;
                  statusController.text = val
                      ? UserStatus.active
                      : UserStatus.inactive;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showEditDialog(BuildContext context) {
    final nameCtrl = TextEditingController(text: nameController.text);
    final designationCtrl = TextEditingController(
      text: designationController.text,
    );
    final emailCtrl = TextEditingController(text: emailController.text);
    final phoneCtrl = TextEditingController(text: phoneController.text);
    final passwordCtrl = TextEditingController(text: passwordController.text);
    final roleCtrl = TextEditingController(text: roleController.text);
    bool statusActive = statusSwitchValue;

    selectedRole = roleCtrl.text.isNotEmpty ? roleCtrl.text : null;

    // Initialize mutable selectedCustomers list from user model or empty list
    List<String> selectedCustomers = List<String>.from(
      widget.user?.customers ?? [],
    );

    final customersList = Get.find<HomeCtrl>().customers;
    final List<DropdownItem<String>> customerItems = customersList
        .map(
          (c) => DropdownItem<String>(
            value: c.docId, // Use docId here
            label: c.name, // Show name in UI
          ),
        )
        .toList();

    showDialog(
      context: context,
      builder: (ctx) {
        bool isSaving = false;
        return StatefulBuilder(
          builder: (ctx, setState) {
            return AlertDialog(
              backgroundColor: popupBgColor,
              title: const Text('Edit User'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: nameCtrl,
                      decoration: const InputDecoration(labelText: 'Name'),
                    ),
                    TextField(
                      controller: designationCtrl,
                      decoration: const InputDecoration(
                        labelText: 'Designation',
                      ),
                    ),
                    TextField(
                      controller: emailCtrl,
                      decoration: const InputDecoration(labelText: 'Email'),
                    ),
                    TextField(
                      controller: phoneCtrl,
                      decoration: const InputDecoration(labelText: 'Phone'),
                    ),
                    SizedBox(height: 10),
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: "Role",
                        border: OutlineInputBorder(),
                      ),
                      value: selectedRole,
                      onChanged: (val) => setState(() => selectedRole = val),
                      items: roles
                          .map(
                            (role) => DropdownMenuItem(
                              value: role,
                              child: Text(role),
                            ),
                          )
                          .toList(),
                    ),
                    SizedBox(height: 10),
                    // TextField(
                    //   controller: roleCtrl,
                    //   decoration: const InputDecoration(labelText: 'Role'),
                    // ),
                    MultiSelectButtonFormField(
                      labelText: 'Customers',
                      items: customerItems,
                      initialValue: selectedCustomers,
                      onChanged: (selectedList) {
                        setState(() {
                          selectedCustomers = selectedList;
                        });
                      },
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                      ),
                      chipDecoration: const ChipDecoration(
                        padding: EdgeInsets.all(20),
                        labelStyle: TextStyle(color: Colors.black),
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        const Text('Status:'),
                        const SizedBox(width: 8),
                        Switch(
                          value: statusActive,
                          onChanged: (val) {
                            setState(() {
                              statusActive = val;
                            });
                          },
                          activeColor: Colors.green,
                          inactiveThumbColor: Colors.red,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          statusActive
                              ? UserStatus.active
                              : UserStatus.inactive,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: isSaving
                      ? null
                      : () {
                          Navigator.pop(ctx);
                        },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isSaving
                      ? null
                      : () async {
                          setState(() => isSaving = true);
                          roleCtrl.text =
                              selectedRole?.trim().isNotEmpty == true
                              ? selectedRole!
                              : roleCtrl.text;

                          await _saveDetailsToFirebase(
                            name: nameCtrl.text,
                            designation: designationCtrl.text,
                            email: emailCtrl.text,
                            phone: phoneCtrl.text,
                            password: passwordCtrl.text,
                            role: roleCtrl.text,
                            status: statusActive
                                ? UserStatus.active
                                : UserStatus.inactive,
                            customers: selectedCustomers,
                          );
                          await Get.find<HomeCtrl>()
                              .getLoggedInUserPermissions();
                          setState(() => isSaving = false);
                          Navigator.pop(ctx);
                        },
                  child: isSaving
                      ? SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2.5,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : const Text('Save'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<bool> _updateUserEmailInAuth(
    String uid,
    String newEmail,
    String currentEmail,
  ) async {
    try {
      final functionName = testMode ? 'testUpdateUserEmail' : 'updateUserEmail';

      final result = await FBFunctions.ff.httpsCallable(functionName).call({
        'uid': uid,
        'newEmail': newEmail,
        'currentEmail': currentEmail,
      });

      final resultData = Map<String, dynamic>.from(result.data);

      if (resultData['success'] == true) {
        print('✅ Email updated in Firebase Auth: ${resultData['msg']}');
        return true;
      } else {
        print(
          '❌ Failed to update email in Firebase Auth: ${resultData['msg']}',
        );
        showCtcAppSnackBar(
          context,
          "Failed to update email: ${resultData['msg']}",
        );
        return false;
      }
    } catch (e) {
      print('❌ Error calling updateUserEmail function: $e');
      showCtcAppSnackBar(context, "Error updating email in authentication");
      return false;
    }
  }

  Widget _buildDetailRowWithIcon({
    required IconData icon,
    required String label,
    required TextEditingController controller,
    required bool editing,
    bool obscure = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 9),
      child: Row(
        children: [
          SizedBox(
            width: 36,
            child: Icon(icon, color: Colors.blueGrey.shade700, size: 24),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: editing
                ? TextField(
                    controller: controller,
                    obscureText: obscure,
                    decoration: InputDecoration(
                      isDense: true,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 10,
                      ),
                    ),
                  )
                : Text(
                    obscure ? '••••••••' : controller.text,
                    style: const TextStyle(fontSize: 16, color: Colors.black87),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRowWithSwitch({
    required IconData icon,
    required String label,
    required TextEditingController controller,
    required bool editing,
    required bool switchValue,
    required ValueChanged<bool> onSwitchChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          SizedBox(
            width: 36,
            child: Icon(icon, color: Colors.blueGrey.shade700, size: 24),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: editing
                ? Row(
                    children: [
                      Text(
                        switchValue ? UserStatus.active : UserStatus.inactive,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Switch(
                        value: switchValue,
                        onChanged: onSwitchChanged,
                        activeColor: Colors.green,
                        inactiveThumbColor: Colors.red,
                        inactiveTrackColor: Colors.red[200],
                      ),
                    ],
                  )
                : Text(
                    controller.text,
                    style: const TextStyle(fontSize: 16, color: Colors.black87),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
          ),
        ],
      ),
    );
  }
}
