import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:legacy_pms/controller/homectrl.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  String? selectedoffice;
  final titleController = TextEditingController();
  final descriptionController = TextEditingController();
  bool allUsers = false;
  bool notiLoading = false;
  bool isTestEnabled = false;
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(10),
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(25),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 20),
                GetBuilder<HomeCtrl>(
                  builder: (ctrl) {
                    return Column(
                      children: [
                        // TextField(
                        //   controller: titleController,
                        //   decoration: const InputDecoration(
                        //     hintText: 'Enter title',
                        //   ),
                        // ),
                        // const SizedBox(height: 16),
                        // TextField(
                        //   controller: descriptionController,
                        //   decoration: const InputDecoration(
                        //     hintText: 'Enter description',
                        //   ),
                        // ),
                        // const SizedBox(height: 16),
                        // Wrap(
                        //   spacing: 20,
                        //   runSpacing: 20,
                        //   alignment: WrapAlignment.start,
                        //   runAlignment: WrapAlignment.start,
                        //   crossAxisAlignment: WrapCrossAlignment.center,
                        //   direction: Axis.horizontal,
                        //   children: [
                        //     Row(
                        //       mainAxisSize: MainAxisSize.min,
                        //       children: [
                        //         const Text('All Users'),
                        //         Checkbox(
                        //           value: allUsers,
                        //           onChanged: (value) {
                        //             if (value == null) {
                        //               return;
                        //             }
                        //             setState(() {
                        //               allUsers = value;
                        //               selectedoffice = null;
                        //             });
                        //           },
                        //         ),
                        //       ],
                        //     ),
                        //     // if (!allUsers)
                        //     //   SizedBox(
                        //     //     // width: 300,
                        //     //     child: DropdownButtonHideUnderline(
                        //     //       child: DropdownButtonFormField(
                        //     //         focusColor: Colors.transparent,
                        //     //         dropdownColor: Colors.white,
                        //     //         decoration: InputDecoration(
                        //     //           hintText: "Select DO",
                        //     //           hintStyle: TextStyle(fontSize: 30),
                        //     //           constraints: const BoxConstraints(
                        //     //             maxWidth: 250,
                        //     //             maxHeight: 45,
                        //     //           ),
                        //     //           border: OutlineInputBorder(
                        //     //             borderRadius: BorderRadius.circular(5),
                        //     //           ),
                        //     //         ),
                        //     //         value: selectedoffice,
                        //     //         items: [
                        //     //           // ...List.generate(
                        //     //           //   ctrl.districtoffice.length,
                        //     //           //   (index) {
                        //     //           //     return DropdownMenuItem(
                        //     //           //       value: ctrl
                        //     //           //           .districtoffice[index]
                        //     //           //           .docId,
                        //     //           //       child: Text(
                        //     //           //         ctrl.districtoffice[index].name,
                        //     //           //       ),
                        //     //           //     );
                        //     //           //   },
                        //     //           // ),
                        //     //         ],
                        //     //         onChanged: (value) async {
                        //     //           setState(() {
                        //     //             // selectedoffice = value;
                        //     //           });
                        //     //         },
                        //     //       ),
                        //     //     ),
                        //     //   ),
                        //     Row(
                        //       mainAxisSize: MainAxisSize.min,
                        //       children: [
                        //         const Text('Test'),
                        //         Switch(
                        //           value: isTestEnabled,
                        //           onChanged: (value) {
                        //             setState(() {
                        //               isTestEnabled = value;
                        //             });
                        //           },
                        //         ),
                        //       ],
                        //     ),
                        //     notiLoading
                        //         ? CircularProgressIndicator()
                        //         : ElevatedButton(
                        //             onPressed: () async {
                        //               setState(() {
                        //                 notiLoading = true;
                        //               });
                        //               try {
                        //                 if (titleController.text.isEmpty ||
                        //                     descriptionController
                        //                         .text
                        //                         .isEmpty) {
                        //                   setState(() {
                        //                     notiLoading = false;
                        //                   });
                        //                   showCtcAppSnackBar(
                        //                     context,
                        //                     "Please enter all the details",
                        //                   );
                        //                   return;
                        //                 }
                        //                 if ((!allUsers) &&
                        //                     selectedoffice == null) {
                        //                   ScaffoldMessenger.of(
                        //                     context,
                        //                   ).showSnackBar(
                        //                     const SnackBar(
                        //                       content: Row(
                        //                         children: [
                        //                           Icon(
                        //                             CupertinoIcons
                        //                                 .exclamationmark_octagon,
                        //                             color: Colors.redAccent,
                        //                           ),
                        //                           SizedBox(width: 10),
                        //                           Text(
                        //                             'No District Office or Users selected',
                        //                           ),
                        //                         ],
                        //                       ),
                        //                     ),
                        //                   );
                        //                 }
                        //                 final String topic = allUsers
                        //                     ? 'global'
                        //                     : selectedoffice!.substring(0, 6);
                        //                 await FBFireStore.customnotifications
                        //                     .add({
                        //                       'title': titleController.text,
                        //                       'desc':
                        //                           descriptionController.text,
                        //                       'topic': topic,
                        //                       'createdAt': Timestamp.now(),
                        //                       'test': isTestEnabled,
                        //                     });
                        //                 if (context.mounted) {
                        //                   selectedoffice = null;
                        //                   isTestEnabled = false;
                        //                   titleController.clear();
                        //                   descriptionController.clear();
                        //                   setState(() {
                        //                     notiLoading = false;
                        //                   });
                        //                 }
                        //               } catch (e) {
                        //                 setState(() {
                        //                   notiLoading = false;
                        //                 });
                        //                 debugPrint(e.toString());
                        //               }
                        //             },
                        //             child: const Text('Push Notification'),
                        //           ),
                        //   ],
                        // ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
