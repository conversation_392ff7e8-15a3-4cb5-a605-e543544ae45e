// ignore_for_file: use_build_context_synchronously

import 'dart:io';
import 'package:excel/excel.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:go_router/go_router.dart';
import 'package:legacy_pms/models/masterqcmodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/firebase.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:path_provider/path_provider.dart';

class MasterQCPage extends StatefulWidget {
  const MasterQCPage({super.key});
  @override
  State<MasterQCPage> createState() => _MasterQCPageState();
}

class _MasterQCPageState extends State<MasterQCPage> {
  List<Masterqcmodel> masterQCs = [];
  bool isLoading = true;
  bool isAdding = false;
  bool isEditing = false;
  Masterqcmodel? editingQC;
  bool isQcExporting = false;

  final TextEditingController titleController = TextEditingController();

  final List<Map<String, dynamic>> selectedInputs = [];

  List<Map<String, dynamic>> availableInputs = [];
  bool isInputsLoading = false;

  @override
  void initState() {
    super.initState();
    _loadMasterQCs();
  }

  Future<void> _loadMasterQCs() async {
    setState(() => isLoading = true);
    try {
      final snapshot = await FBFireStore.masterQc.get();
      if (!mounted) return;
      setState(() {
        masterQCs = snapshot.docs
            .map((doc) => Masterqcmodel.fromSnap(doc))
            .toList();
        isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        showSnackBar('Failed to load master QCs: $e');
      }
    }
  }

  Future<void> _loadAvailableInputs() async {
    setState(() => isInputsLoading = true);
    try {
      final fieldDocs = await FBFireStore.textfield.get();
      final yesnoDocs = await FBFireStore.yesNO.get();
      final List<Map<String, dynamic>> allInputs = [];
      for (var doc in fieldDocs.docs) {
        final map = Map<String, dynamic>.from(doc.data());
        map['docId'] = doc.id; // ensure unique docId included
        allInputs.add(map);
      }
      for (var doc in yesnoDocs.docs) {
        final map = Map<String, dynamic>.from(doc.data());
        map['docId'] = doc.id; // ensure unique docId included
        allInputs.add(map);
      }
      if (!mounted) return;
      setState(() {
        availableInputs = allInputs;
        isInputsLoading = false;
      });
    } catch (e) {
      if (mounted) {
        setState(() => isInputsLoading = false);
        showSnackBar('Failed to load available QC inputs: $e');
      }
    }
  }

  void _showAddDialog() async {
    _resetForm();
    setState(() {
      isAdding = true;
      isEditing = false;
      editingQC = null;
    });
    await _loadAvailableInputs();
    _showQCDialog();
  }

  void _showEditDialog(Masterqcmodel qc) async {
    _resetForm();
    titleController.text = qc.title;

    selectedInputs.clear();
    if (qc.qcInputModel != null) {
      for (var input in qc.qcInputModel!) {
        if (input is Map) {
          selectedInputs.add(Map<String, dynamic>.from(input));
        }
      }
    }

    setState(() {
      isEditing = true;
      isAdding = false;
      editingQC = qc;
    });

    await _loadAvailableInputs();

    _showQCDialog();
  }

  void _resetForm() {
    titleController.clear();
    selectedInputs.clear();
    availableInputs.clear();
  }

  void _showAddQCInputDialog(StateSetter setDialogState) {
    String? selectedType;
    final TextEditingController questionController = TextEditingController();
    bool yesNoDefault = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogStateInner) => AlertDialog(
          backgroundColor: popupBgColor,
          title: const Text('Add QC Input'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: selectedType,
                items: inputTypeMap.keys
                    .map(
                      (type) =>
                          DropdownMenuItem(value: type, child: Text(type)),
                    )
                    .toList(),
                onChanged: (val) =>
                    setDialogStateInner(() => selectedType = val),
                decoration: const InputDecoration(labelText: 'Input Type'),
              ),
              if (selectedType != null) ...[
                const SizedBox(height: 12),
                TextField(
                  controller: questionController,
                  decoration: const InputDecoration(
                    labelText: 'Question',
                    border: OutlineInputBorder(),
                  ),
                ),
                if (selectedType == inputTypeMap["Yes/No"])
                  Row(
                    children: [
                      const Text("Default:"),
                      Checkbox(
                        value: yesNoDefault,
                        onChanged: (val) {
                          setDialogStateInner(
                            () => yesNoDefault = val ?? false,
                          );
                        },
                      ),
                      Text(yesNoDefault ? "Yes" : "No"),
                    ],
                  ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => context.pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (selectedType == null ||
                    questionController.text.trim().isEmpty) {
                  // Show error
                  return;
                }
                final now = DateTime.now();
                final inputData = {
                  "title": questionController.text.trim(),
                  "createdAt": Timestamp.fromDate(now),
                  "type": selectedType == "Yes/No" ? "yesno" : "textfield",
                };
                if (selectedType == "Yes/No") {
                  inputData["yes"] = yesNoDefault;
                }
                try {
                  final CollectionReference inputCollection =
                      (selectedType == "Yes/No")
                      ? FBFireStore.yesNO
                      : FBFireStore.textfield;
                  final docRef = await inputCollection.add(inputData);
                  final inputMap = Map<String, dynamic>.from(inputData);
                  inputMap["docId"] = docRef.id;
                  setDialogState(() {
                    selectedInputs.add(inputMap);
                    availableInputs.add(inputMap);
                  });
                  context.pop();
                } catch (e) {
                  // Handle error
                }
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );
  }

  void _removeQCInput(int index, StateSetter setDialogState) {
    setDialogState(() {
      selectedInputs.removeAt(index);
    });
  }

  void _showQCDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => Dialog(
          backgroundColor: popupBgColor,
          insetPadding: const EdgeInsets.symmetric(
            horizontal: 40,
            vertical: 48,
          ),
          child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.8,
              maxHeight: MediaQuery.of(context).size.height * 0.7,
            ),
            padding: const EdgeInsets.symmetric(vertical: 24),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                // Title
                Text(
                  isAdding ? 'Add Master QC' : 'Edit Master QC',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 24),

                // Form area inside scrollable & expanded area
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Column(
                      children: [
                        SizedBox(height: 10),
                        // Title input
                        TextField(
                          controller: titleController,
                          style: const TextStyle(fontSize: 18),
                          decoration: const InputDecoration(
                            labelText: 'Title',
                            labelStyle: TextStyle(fontSize: 16),
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),

                        // QC Inputs header and Add Input button
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'QC Inputs',
                              style: Theme.of(
                                context,
                              ).textTheme.titleMedium?.copyWith(fontSize: 18),
                            ),
                            ElevatedButton.icon(
                              onPressed: () =>
                                  _showAddQCInputDialog(setDialogState),
                              icon: const Icon(Icons.add),
                              label: const Text('Add Input'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: logoTealColor,
                                foregroundColor: whiteColor,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 12,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),

                        // Show loading indicator or empty message or selected inputs list
                        Builder(
                          builder: (_) {
                            if (isInputsLoading) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            }
                            if (availableInputs.isEmpty &&
                                selectedInputs.isEmpty) {
                              return Container(
                                padding: const EdgeInsets.all(24),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Center(
                                  child: Text(
                                    'No QC inputs added yet. Click "Add Input" to get started.',
                                    style: TextStyle(color: Colors.grey),
                                  ),
                                ),
                              );
                            }

                            // List selected QC inputs with delete icons
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (selectedInputs.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(
                                      top: 8,
                                      bottom: 12,
                                    ),
                                    child: Text(
                                      'Selected QC Inputs:',
                                      style: Theme.of(context)
                                          .textTheme
                                          .labelMedium
                                          ?.copyWith(fontSize: 16),
                                    ),
                                  ),
                                ...List.generate(selectedInputs.length, (idx) {
                                  final input = selectedInputs[idx];
                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: 12),
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Q: ${input['title']}',
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              Text(
                                                'Type: ${input['type']}',

                                                style: const TextStyle(
                                                  fontSize: 16,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        IconButton(
                                          onPressed: () => _removeQCInput(
                                            idx,
                                            setDialogState,
                                          ),
                                          icon: const Icon(Icons.delete),
                                          tooltip: 'Delete Input',
                                        ),
                                      ],
                                    ),
                                  );
                                }),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Buttons row
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {
                          context.pop();
                          _resetForm();
                        },
                        child: const Text(
                          'Cancel',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: _saveQC,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 14,
                          ),
                        ),
                        child: Text(
                          isAdding ? 'Add' : 'Update',
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _saveQC() async {
    if (titleController.text.trim().isEmpty) {
      showSnackBar('Title is required');
      return;
    }
    if (selectedInputs.isEmpty) {
      showSnackBar('Add at least one input');
      return;
    }

    try {
      final data = {
        'title': titleController.text.trim(),
        'qcInputModel': selectedInputs,
        'createdAt': FieldValue.serverTimestamp(),
      };

      if (isAdding) {
        await FBFireStore.masterQc.add(data);
        showSnackBar('Master QC Created');
      } else if (isEditing && editingQC != null) {
        await FBFireStore.masterQc.doc(editingQC!.docId).update(data);
        showSnackBar('Master QC Updated');
      }

      context.pop();
      _resetForm();
      await _loadMasterQCs();
    } catch (e) {
      showSnackBar('Save failed: $e');
    }
  }

  Future<void> _deleteQC(Masterqcmodel qc) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: popupBgColor,
        title: const Text('Confirm Delete'),
        content: Text('Are you sure you want to delete "${qc.title}"?'),
        actions: [
          TextButton(
            onPressed: () => context.pop(false),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () => context.pop(true),
            // style: ElevatedButton.styleFrom(backgroundColor: redColor),
            child: const Text('Yes'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await FBFireStore.masterQc.doc(qc.docId).delete();

        showSnackBar('Master QC deleted successfully');

        // Clear editing state if deleted QC is being edited
        if (editingQC != null && editingQC!.docId == qc.docId) {
          setState(() {
            editingQC = null;
            isEditing = false;
            isAdding = false;
            titleController.clear();
            selectedInputs.clear();
          });
        }

        await _loadMasterQCs();
      } catch (e) {
        showSnackBar('Failed to delete QC: $e');
      }
    }
  }

  // void _showAddInputDialog(StateSetter setDialogState) {
  //   String? selectedType;
  //   final questionController = TextEditingController();
  //   bool yesNoDefault = false;
  //   showDialog(
  //     context: context,
  //     builder: (context) => StatefulBuilder(
  //       builder: (context, setStateInner) => AlertDialog(
  //         title: Text('Add Input'),
  //         content: Column(
  //           mainAxisSize: MainAxisSize.min,
  //           children: [
  //             DropdownButtonFormField<String>(
  //               value: selectedType,
  //               items: ['Textfield', 'YesNo'].map((type) {
  //                 return DropdownMenuItem(value: type, child: Text(type));
  //               }).toList(),
  //               onChanged: (val) => setStateInner(() => selectedType = val),
  //               decoration: InputDecoration(labelText: 'Select Input Type'),
  //             ),
  //             if (selectedType != null) ...[
  //               SizedBox(height: 12),
  //               TextField(
  //                 controller: questionController,
  //                 decoration: InputDecoration(
  //                   labelText: 'Question',
  //                   border: OutlineInputBorder(),
  //                 ),
  //               ),
  //               if (selectedType == 'YesNo')
  //                 Row(
  //                   children: [
  //                     Text('Default:'),
  //                     Checkbox(
  //                       value: yesNoDefault,
  //                       onChanged: (val) =>
  //                           setStateInner(() => yesNoDefault = val ?? false),
  //                     ),
  //                     Text(yesNoDefault ? 'Yes' : 'No'),
  //                   ],
  //                 ),
  //             ],
  //           ],
  //         ),
  //         actions: [
  //           TextButton(onPressed: () => context.pop(), child: Text('Cancel')),
  //           ElevatedButton(
  //             onPressed: () {
  //               if (selectedType == null || questionController.text.isEmpty) {
  //                 // Show error
  //                 return;
  //               }
  //               final now = DateTime.now();
  //               Map<String, dynamic> inputObject;
  //               if (selectedType == 'Textfield') {
  //                 inputObject = {
  //                   'type': 'textfield',
  //                   'title': questionController.text.trim(),
  //                   'answer': null,
  //                   'completed': false,
  //                   'createdAt': now.toIso8601String(),
  //                 };
  //               } else {
  //                 // yesno
  //                 inputObject = {
  //                   'type': 'yesno',
  //                   'title': questionController.text.trim(),
  //                   'yes': yesNoDefault,
  //                   'completed': false,
  //                   'createdAt': now.toIso8601String(),
  //                 };
  //               }
  //               setState(() {
  //                 selectedInputs.add(inputObject);
  //                 availableInputs.add(inputObject);
  //               });
  //               context.pop();
  //             },
  //             child: Text('Add'),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: logoTealColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: logoTealColor.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Master QC Management',
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(
                              color: whiteColor,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Create and manage master QC templates',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: whiteColor.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                isQcExporting
                    ? CircularProgressIndicator()
                    : ElevatedButton.icon(
                        onPressed: () async => await exportMasterQCsToExcel(
                          qcs: masterQCs,
                          context: context,
                        ),
                        // _showAddDialog,
                        icon: const Icon(Icons.add),
                        label: const Text('Export'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: whiteColor,
                          foregroundColor: logoTealColor,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                const SizedBox(width: 10),
                ElevatedButton.icon(
                  onPressed: _showAddDialog,
                  icon: const Icon(Icons.add),
                  label: const Text('Create Master QC'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: whiteColor,
                    foregroundColor: logoTealColor,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 16,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),
          if (isLoading)
            const Center(child: CircularProgressIndicator())
          else if (masterQCs.isEmpty)
            _buildEmptyState()
          else
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: _buildQCList(),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(48),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
        // border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.checklist, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'No Master QCs Available',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first master QC to get started',
              style: TextStyle(color: Colors.grey.shade500),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQCList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Existing Master QCs',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: logoTealColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          color: Colors.grey.shade200,
          child: Row(
            children: const [
              Expanded(
                flex: 1,
                child: Text(
                  "Sr.",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 3,
                child: Text(
                  "Title",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 5,
                child: Text(
                  "QC Inputs",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  "Created",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  "Actions",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
        ),
        const Divider(height: 1),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: masterQCs.length,
          itemBuilder: (context, index) {
            final qc = masterQCs[index];
            return Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
              decoration: BoxDecoration(
                // border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(flex: 1, child: Text("${index + 1}")),
                  Expanded(
                    flex: 3,
                    child: Text(
                      qc.title,
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                  ),
                  Expanded(
                    flex: 5,
                    child:
                        qc.qcInputModel != null && qc.qcInputModel!.isNotEmpty
                        ? Text(
                            qc.qcInputModel!
                                .map(
                                  (e) =>
                                      (e is Map &&
                                          e['type'] != null &&
                                          e['title'] != null)
                                      ? '${e['type']}: ${e['title']}'
                                      : '',
                                )
                                .join(', '),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(color: Colors.grey.shade700),
                          )
                        : const Text('-'),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      _formatDate(qc.createdAt),
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () => _showEditDialog(qc),
                          icon: const Icon(Icons.edit, size: 20),
                          color: logoTealColor,
                          tooltip: 'Edit',
                        ),
                        IconButton(
                          onPressed: () => _deleteQC(qc),
                          icon: const Icon(Icons.delete, size: 20),
                          color: redColor,
                          tooltip: 'Delete',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> exportMasterQCsToExcel({
    required List<Masterqcmodel> qcs,
    required BuildContext context,
  }) async {
    setState(() {
      isQcExporting = true;
    });

    try {
      final excel = Excel.createExcel();
      // Remove default sheet
      excel.delete('Sheet1');

      final boldStyle = CellStyle(bold: true);

      for (var i = 0; i < qcs.length; i++) {
        final qc = qcs[i];

        final sheetName = 'QC ${i + 1}'; // Sheet per Sr.No
        final sheet = excel[sheetName];

        // Title row
        sheet.appendRow([TextCellValue('Title'), TextCellValue(qc.title)]);
        sheet
                .cell(
                  CellIndex.indexByColumnRow(
                    columnIndex: 0,
                    rowIndex: sheet.maxRows - 1,
                  ),
                )
                .cellStyle =
            boldStyle;

        // Created Date row
        sheet.appendRow([
          TextCellValue('Created Date'),
          TextCellValue(_formatDate(qc.createdAt)),
        ]);
        sheet
                .cell(
                  CellIndex.indexByColumnRow(
                    columnIndex: 0,
                    rowIndex: sheet.maxRows - 1,
                  ),
                )
                .cellStyle =
            boldStyle;

        // Blank row
        sheet.appendRow([]);

        // QC Inputs label
        sheet.appendRow([TextCellValue('QC Inputs:')]);
        sheet
                .cell(
                  CellIndex.indexByColumnRow(
                    columnIndex: 0,
                    rowIndex: sheet.maxRows - 1,
                  ),
                )
                .cellStyle =
            boldStyle;

        // QC input titles
        if (qc.qcInputModel != null && qc.qcInputModel!.isNotEmpty) {
          for (var input in qc.qcInputModel!) {
            if (input is Map && input['title'] != null) {
              sheet.appendRow([TextCellValue(input['title'])]);
            }
          }
        }

        // Blank row for spacing
        sheet.appendRow([]);
      }

      final excelBytes = excel.encode();
      final filename =
          'MasterQCs_${DateTime.now().millisecondsSinceEpoch}.xlsx';

      if (kIsWeb) {
        await FileSaver.instance.saveFile(
          name: filename,
          bytes: Uint8List.fromList(excelBytes!),
          fileExtension: 'xlsx',
        );
        showSnackBar('Exported QCs to $filename');
      } else {
        final dir = await getExternalStorageDirectory();
        final filePath = '${dir!.path}/$filename';
        final file = File(filePath);
        await file.writeAsBytes(excelBytes!);
        showSnackBar('Exported QCs to $filePath');
      }
    } catch (e) {
      showSnackBar('Export failed: $e');
    } finally {
      setState(() {
        isQcExporting = false;
      });
    }
  }
}
  // void _toggleQCInput(Map<String, dynamic> input, StateSetter setDialogState) {
  //   final int index = selectedInputs.indexWhere(
  //     (i) => i['docId'] == input['docId'],
  //   );
  //   if (index == -1) {
  //     setDialogState(() {
  //       // Add a copy to avoid referencing issues
  //       selectedInputs.add(Map<String, dynamic>.from(input));
  //     });
  //   } else {
  //     setDialogState(() {
  //       selectedInputs.removeAt(index);
  //     });
  //   }
  // }
