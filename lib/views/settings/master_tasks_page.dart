import 'dart:io';
import 'package:excel/excel.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/models/masterqcmodel.dart';
import 'package:legacy_pms/models/mastertaskmodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/firebase.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:path_provider/path_provider.dart';

class MasterTasksPage extends StatefulWidget {
  const MasterTasksPage({super.key});

  @override
  State<MasterTasksPage> createState() => _MasterTasksPageState();
}

class _MasterTasksPageState extends State<MasterTasksPage> {
  List<Mastertaskmodel> masterTasks = [];
  bool isLoading = true;
  bool isAdding = false;
  bool isEditing = false;
  Mastertaskmodel? editingTask;
  bool showCount = false;

  // Form controllers
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descController = TextEditingController();
  final TextEditingController benchmarkController = TextEditingController();
  String selectedType = 'd';
  String? selectedQc;
  bool isMTExporting = false;

  @override
  void initState() {
    super.initState();
    _loadMasterTasks();
  }

  Future<void> _loadMasterTasks() async {
    setState(() {
      isLoading = true;
    });

    try {
      final snapshot = await FBFireStore.masterTasks.get();
      setState(() {
        masterTasks = snapshot.docs
            .map((doc) => Mastertaskmodel.fromSnap(doc))
            .toList();
        masterTasks.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      showSnackBar('Failed to load master tasks: $e');
    }
  }

  void _showAddDialog(HomeCtrl ctrl) {
    _resetForm();
    setState(() {
      isAdding = true;
      isEditing = false;
      editingTask = null;
    });
    _showTaskDialog(ctrl);
  }

  void _showEditDialog(Mastertaskmodel task, HomeCtrl ctrl) {
    _resetForm();
    titleController.text = task.title;
    descController.text = task.desc;
    benchmarkController.text = task.benchmark.toString();
    selectedType = task.type;
    showCount = task.showCount;
    if (ctrl.masterQcs.any((qc) => qc.docId == task.masterQc)) {
      selectedQc = task.masterQc;
    } else {
      selectedQc = null; // or handle default qc null
    }

    setState(() {
      isEditing = true;
      isAdding = false;
      editingTask = task;
    });
    _showTaskDialog(ctrl);
  }

  void _resetForm() {
    titleController.clear();
    descController.clear();
    benchmarkController.clear();
    selectedType = 'd';
    showCount = false;
  }

  void _showTaskDialog(HomeCtrl ctrl) {
    if (ctrl.masterQcs.isEmpty) {
      selectedQc = null;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState2) {
          return AlertDialog(
            backgroundColor: popupBgColor,
            insetPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 20,
            ),

            title: Text(isAdding ? 'Add Master Task' : 'Edit Master Task'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: titleController,
                    decoration: const InputDecoration(
                      labelText: 'Title',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: descController,
                    maxLines: 2,
                    decoration: const InputDecoration(
                      labelText: 'Description',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: benchmarkController,
                    // maxLines: 3,
                    decoration: const InputDecoration(
                      labelText: 'Benchmark',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: selectedType,
                    decoration: const InputDecoration(
                      labelText: 'Type',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'd', child: Text('Daily')),
                      DropdownMenuItem(value: 'w', child: Text('Weekly')),
                      DropdownMenuItem(value: 'm', child: Text('Monthly')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        selectedType = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),

                  DropdownButtonFormField(
                    value: selectedQc,
                    decoration: const InputDecoration(
                      labelText: 'Select Qc',
                      border: OutlineInputBorder(),
                    ),
                    items: ctrl.masterQcs.isEmpty
                        ? null
                        : ctrl.masterQcs
                              .map(
                                (qc) => DropdownMenuItem(
                                  value: qc.docId,
                                  child: Text(qc.title),
                                ),
                              )
                              .toList(),
                    onChanged: (value) {
                      setState(() {
                        selectedQc = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Text("Show Count on Dashboard"),
                      Checkbox(
                        // checkColor: logoTealColor,
                        activeColor: logoTealColor,
                        value: showCount,
                        onChanged: (value) {
                          setState2(() {
                            showCount = value!;
                            // print("showCount: $showCount");
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  // Navigator.of(context).pop();
                  context.pop();
                  _resetForm();
                },
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: logoTealColor,
                  foregroundColor: whiteColor,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 28,
                    vertical: 13,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  textStyle: const TextStyle(fontWeight: FontWeight.bold),
                ),
                onPressed: _saveTask,
                child: Text(isAdding ? 'Add' : 'Update'),
              ),
            ],
          );
        },
      ),
    );
  }

  Future<void> _saveTask() async {
    if (titleController.text.trim().isEmpty ||
        descController.text.trim().isEmpty ||
        benchmarkController.text.trim().isEmpty ||
        selectedQc == null) {
      showSnackBar('Please fill in all fields');
      return;
    }

    try {
      if (isAdding) {
        final newTask = {
          'title': titleController.text.trim(),
          'desc': descController.text.trim(),
          'type': selectedType,
          'benchmark': benchmarkController.text.trim(),
          'createdAt': DateTime.now(),
          'showCount': showCount,
          'masterQc': selectedQc,
        };
        await FBFireStore.masterTasks.add(newTask);
        // await FirebaseUtils.createMasterTask(newTask);
        showSnackBar('Master task created successfully');
      } else if (isEditing && editingTask != null) {
        final updatedTask = {
          'title': titleController.text.trim(),
          'desc': descController.text.trim(),
          'type': selectedType,
          'benchmark': benchmarkController.text.trim(),
          'showCount': showCount,
          'masterQc': selectedQc,
        };
        // await FirebaseUtils.updateMasterTask(editingTask!.docId, updatedTask);
        await FBFireStore.masterTasks
            .doc(editingTask!.docId)
            .update(updatedTask);
        showSnackBar('Master task updated successfully');
      }

      // Navigator.of(context).pop();
      context.pop();
      _resetForm();
      _loadMasterTasks();
    } catch (e) {
      showSnackBar('Failed to save task: $e');
    }
  }

  Future<void> _deleteTask(Mastertaskmodel task) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: popupBgColor,
        title: const Text('Confirm Delete'),
        content: Text('Are you sure you want to delete "${task.title}"?'),
        actions: [
          TextButton(
            onPressed: () =>
                //  Navigator.of(context).pop(false),
                context.pop(false),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () =>
                // Navigator.of(context).pop(true),
                context.pop(true),
            // style: ElevatedButton.styleFrom(backgroundColor: redColor),
            child: const Text('Yes'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // await FirebaseUtils.deleteMasterTask(task.docId);
        await FBFireStore.masterTasks.doc(task.docId).delete();
        showSnackBar('Master task deleted successfully');
        _loadMasterTasks();
      } catch (e) {
        showSnackBar('Failed to delete task: $e');
      }
    }
  }

  String _getTypeLabel(String type) {
    switch (type) {
      case 'd':
        return 'Daily';
      case 'w':
        return 'Weekly';
      case 'm':
        return 'Monthly';
      default:
        return 'Unknown';
    }
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'd':
        return greenColor;
      case 'w':
        return orangeColor;
      case 'm':
        return logoTealColor;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: logoTealColor,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: logoTealColor.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Master Tasks Management',
                            style: Theme.of(context).textTheme.headlineSmall
                                ?.copyWith(
                                  color: whiteColor,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Create and manage master task templates',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: whiteColor.withOpacity(0.9)),
                          ),
                        ],
                      ),
                    ),
                    isMTExporting
                        ? CircularProgressIndicator()
                        : ElevatedButton.icon(
                            onPressed: () async =>
                                await exportMasterTasksToExcel(
                                  tasks: masterTasks,
                                  context: context,
                                  ctrl: ctrl,
                                  isExporting: isMTExporting,
                                ),
                            // _showAddDialog,
                            icon: const Icon(Icons.add),
                            label: const Text('Export'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: whiteColor,
                              foregroundColor: logoTealColor,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 16,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                    const SizedBox(width: 10),
                    ElevatedButton.icon(
                      onPressed: () => _showAddDialog(ctrl),
                      icon: const Icon(Icons.add),
                      label: const Text('Create Master Task'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: whiteColor,
                        foregroundColor: logoTealColor,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 16,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Tasks list
              if (isLoading)
                const Center(child: CircularProgressIndicator())
              else if (masterTasks.isEmpty)
                _buildEmptyState()
              else
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: _buildTasksList(ctrl),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(48),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
        // border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.task_alt, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'No Master Tasks Available',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first master task to get started',
              style: TextStyle(color: Colors.grey.shade500),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTasksList(HomeCtrl ctrl) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Existing Master Tasks',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: logoTealColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Table Header
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          color: logoTealColor.withOpacity(0.1),
          child: Row(
            children: const [
              Expanded(flex: 1, child: Text("Sr.")),
              Expanded(flex: 2, child: Text("Title")),
              Expanded(flex: 2, child: Text("Description")),
              Expanded(flex: 2, child: Text("Benchmark")),
              Expanded(flex: 2, child: Text("Type")),
              Expanded(flex: 2, child: Center(child: Text("Actions"))),
            ],
          ),
        ),
        const SizedBox(height: 8),
        // List with Data
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: masterTasks.length,
          itemBuilder: (context, index) {
            final task = masterTasks[index];
            return Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
              decoration: BoxDecoration(
                // border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Row(
                children: [
                  Expanded(flex: 1, child: Text("${index + 1}")),
                  Expanded(flex: 2, child: Text(task.title)),
                  Expanded(flex: 2, child: Text(task.desc)),
                  Expanded(flex: 2, child: Text(task.benchmark)),
                  Expanded(
                    flex: 2,
                    child: Text(
                      _getTypeLabel(task.type),
                      style: TextStyle(color: _getTypeColor(task.type)),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Transform.scale(
                          scale: 0.8,
                          child: CupertinoSwitch(
                            inactiveTrackColor: Colors.grey.shade400,
                            value: task.showCount,
                            onChanged: (value) async {
                              setState(() {
                                task.showCount = value;
                              });
                              await FBFireStore.masterTasks
                                  .doc(task.docId)
                                  .update({'showCount': value});
                            },
                          ),
                        ),
                        IconButton(
                          onPressed: () => _showEditDialog(task, ctrl),
                          icon: const Icon(Icons.edit),
                          color: logoTealColor,
                        ),
                        IconButton(
                          onPressed: () => _deleteTask(task),
                          icon: const Icon(Icons.delete),
                          color: redColor,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Future<void> exportMasterTasksToExcel({
    required List<Mastertaskmodel> tasks,
    required BuildContext context,
    required HomeCtrl ctrl,
    required bool isExporting,
  }) async {
    setState(() {
      isExporting = true;
    });
    try {
      // Optional: Show a loading indicator or set a flag if needed
      final excel = Excel.createExcel();
      final sheet = excel['Sheet1'];

      final headers = [
        'Sr.No',
        'Title',
        'Description',
        'Benchmark',
        'Type',
        // 'Show Count',
        'QC',
        // 'Created At',
      ];

      sheet.appendRow(headers.map((h) => TextCellValue(h)).toList());

      for (var i = 0; i < tasks.length; i++) {
        final task = tasks[i];
        final row = [
          TextCellValue((i + 1).toString()),
          TextCellValue(task.title),
          TextCellValue(task.desc),
          TextCellValue(task.benchmark),
          TextCellValue(_getTypeLabel(task.type)),
          // TextCellValue(task.showCount ? 'Yes' : 'No'),
          TextCellValue(
            ctrl.masterQcs
                    .firstWhere(
                      (qc) => qc.docId == task.masterQc,
                      orElse: () => Masterqcmodel(
                        docId: '',
                        title: '',
                        createdAt: DateTime.now(),
                        qcInputModel: [],
                      ),
                    )
                    .title ??
                '',
          ),
          // TextCellValue(
          //   task.createdAt != null
          //       ? DateFormat('dd-MM-yyyy').format(task.createdAt)
          //       : '',
          // ),
        ];
        sheet.appendRow(row);
      }

      final excelBytes = excel.encode();
      final filename =
          'MasterTasks_${DateTime.now().millisecondsSinceEpoch}.xlsx';

      if (kIsWeb) {
        await FileSaver.instance.saveFile(
          name: filename,
          bytes: Uint8List.fromList(excelBytes!),
          fileExtension: 'xlsx',
        );
        showSnackBar('Exported tasks to $filename');
        setState(() {
          isExporting = false;
        });
      } else {
        final dir = await getExternalStorageDirectory();
        final filePath = '${dir!.path}/$filename';
        final file = File(filePath);
        await file.writeAsBytes(excelBytes!);
        showSnackBar('Exported tasks to $filePath');
        setState(() {
          isExporting = false;
        });
      }
    } catch (e) {
      debugPrint(e.toString());
      setState(() {
        isExporting = false;
      });
      showSnackBar('Failed to export tasks: $e');
    }
  }

  // String _formatDate(DateTime date) {
  //   return '${date.day}/${date.month}/${date.year}';
  // }
}
