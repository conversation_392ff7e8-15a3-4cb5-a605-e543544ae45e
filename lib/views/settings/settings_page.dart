import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:legacy_pms/shared/router.dart';

// Brand Colors
const orangeColor = Color(0xFFEE8023);
const logoTealColor = Color(0xFF36748A);
const whiteColor = Colors.white;

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Wrap(
        spacing: 20, // horizontal spacing
        runSpacing: 20, // vertical spacing when wrapping
        children: [
          SettingsCard(
            containerName: "Create Master Tasks",
            icon: Icons.task,
            onTap: () => context.push(Routes.mastertasks),
          ),
          SettingsCard(
            containerName: "Create Master QC",
            icon: Icons.checklist,
            onTap: () => context.push(Routes.masterqc),
          ),
          SettingsCard(
            containerName: "Create Role",
            icon: Icons.admin_panel_settings,
            onTap: () {
              context.push(Routes.createrole);
            },
          ),
          SettingsCard(
            containerName: "Tasks History",
            icon: Icons.history,
            onTap: () {
              context.push(Routes.tasksHistory);
            },
          ),

          // You can add more cards here as needed
        ],
      ),
    );
  }
}

class SettingsCard extends StatelessWidget {
  final String containerName;
  final IconData icon;
  final VoidCallback? onTap;

  const SettingsCard({
    super.key,
    required this.containerName,
    required this.icon,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    final double cardWidth = width > 600 ? width * 0.22 : width * 0.40;

    return InkWell(
      borderRadius: BorderRadius.circular(16),
      onTap: onTap ?? () {},
      child: Card(
        color: whiteColor,
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
        shadowColor: logoTealColor.withOpacity(0.25),
        child: Container(
          width: cardWidth,
          height: 160,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14),
            border: Border.all(
              color: logoTealColor.withOpacity(0.15),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 48, color: logoTealColor),
              const SizedBox(height: 16),
              Text(
                containerName,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: 16,
                  color: logoTealColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// class CreateRoleDialog extends StatefulWidget {
//   const CreateRoleDialog({super.key});

//   @override
//   State<CreateRoleDialog> createState() => _CreateRoleDialogState();
// }

// class _CreateRoleDialogState extends State<CreateRoleDialog> {
//   final TextEditingController _roleController = TextEditingController();

//   bool _isSubmitting = false;
//   List<String> selectedPermissions = [];
//   String? deletingDocId;

//   Future<void> addRole() async {
//     final roleName = _roleController.text.trim();
//     if (roleName.isEmpty) {
//       showSnackBar("Please enter a role name.");
//       setState(() => _isSubmitting = false);

//       return;
//     }

//     setState(() => _isSubmitting = true);

//     try {
//       await FBFireStore.roles.add({
//         'title': roleName,
//         'createdAt': FieldValue.serverTimestamp(),
//         'permissions': selectedPermissions ?? [],
//       });

//       _roleController.clear();
//       showSnackBar("Role added successfully.");
//       context.pop();
//     } catch (e) {
//       showSnackBar("Failed to add role: $e");
//     }

//     setState(() => _isSubmitting = false);
//   }

//   Future<void> deleteRole(String docId) async {
//     setState(() {
//       deletingDocId = docId;
//     });

//     try {
//       await FBFireStore.roles.doc(docId).delete();
//       showSnackBar("Role deleted successfully");
//     } catch (e) {
//       showSnackBar("Failed to delete role: $e");
//     }

//     setState(() {
//       deletingDocId = null;
//     });
//   }

//   @override
//   void dispose() {
//     _roleController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final bool isWide = MediaQuery.of(context).size.width > 600;
//     final double dialogWidth = isWide ? 480 : double.infinity;

//     return Dialog(
//       backgroundColor: Colors.white,
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(2)),
//       insetPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
//       child: ConstrainedBox(
//         constraints: BoxConstraints(maxWidth: dialogWidth),
//         child: Padding(
//           padding: const EdgeInsets.only(
//             top: 24,
//             left: 24,
//             right: 24,
//             bottom: 0,
//           ),
//           child: Column(
//             mainAxisSize: MainAxisSize.min,
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 'Create Role',
//                 style: Theme.of(
//                   context,
//                 ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700),
//               ),
//               const SizedBox(height: 16),
//               TextField(
//                 controller: _roleController,
//                 autocorrect: false,
//                 textCapitalization: TextCapitalization.words,
//                 decoration: InputDecoration(
//                   labelText: 'Role Name',
//                   hintText: 'Enter the new role name',
//                   border: OutlineInputBorder(
//                     borderRadius: BorderRadius.circular(2),
//                   ),
//                   contentPadding: const EdgeInsets.symmetric(
//                     horizontal: 16,
//                     vertical: 14,
//                   ),
//                 ),
//               ),
//               const SizedBox(height: 20),
//               SettingsPermissionMultiSelectDropdown(
//                 title: "Permissions",
//                 options: permissions,
//                 selected: selectedPermissions,
//                 onChanged: (newList) =>
//                     setState(() => selectedPermissions = newList),
//               ),
//               const SizedBox(height: 20),
//               SizedBox(
//                 width: double.infinity,
//                 child: ElevatedButton(
//                   onPressed: _isSubmitting ? null : addRole,
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: const Color(0xFF4CAF50),
//                     padding: const EdgeInsets.symmetric(vertical: 14),
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(2),
//                     ),
//                     textStyle: const TextStyle(
//                       fontSize: 16,
//                       fontWeight: FontWeight.w600,
//                     ),
//                   ),
//                   child: _isSubmitting
//                       ? const SizedBox(
//                           height: 20,
//                           width: 20,
//                           child: CircularProgressIndicator(
//                             color: Colors.white,
//                             strokeWidth: 2.5,
//                           ),
//                         )
//                       : const Text(
//                           'Add Role',
//                           style: TextStyle(
//                             color: Colors.white,
//                             fontWeight: FontWeight.w200,
//                           ),
//                         ),
//                 ),
//               ),
//               const SizedBox(height: 24),
//               const Divider(thickness: 1),
//               const SizedBox(height: 12),
//               Text(
//                 'Existing Roles',
//                 style: Theme.of(
//                   context,
//                 ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
//               ),
//               const SizedBox(height: 12),
//               SizedBox(
//                 height: 220,
//                 child: StreamBuilder<QuerySnapshot>(
//                   stream: FBFireStore.roles
//                       .orderBy('createdAt', descending: true)
//                       .snapshots(),
//                   builder: (context, snapshot) {
//                     if (snapshot.hasError) {
//                       return const Center(child: Text('Error loading roles.'));
//                     }
//                     if (snapshot.connectionState == ConnectionState.waiting) {
//                       return const Center(child: CircularProgressIndicator());
//                     }
//                     final docs = snapshot.data!.docs;
//                     if (docs.isEmpty) {
//                       return const Center(child: Text('No roles created yet.'));
//                     }
//                     return Scrollbar(
//                       child: ListView.separated(
//                         itemCount: docs.length,
//                         separatorBuilder: (_, __) => const Divider(height: 1),
//                         itemBuilder: (context, index) {
//                           final roleData =
//                               docs[index].data()! as Map<String, dynamic>;
//                           final title = roleData['title'] ?? 'Unnamed Role';
//                           return ListTile(
//                             leading: const Icon(Icons.admin_panel_settings),
//                             title: Text(title),
//                             trailing: IconButton(
//                               icon: Icon(Icons.cancel_outlined),
//                               color: Colors.red,
//                               onPressed: () async => deleteRole(docs[index].id),
//                               // {
//                               //   try {
//                               //     await FBFireStore.roles
//                               //         .doc(docs[index].id)
//                               //         .delete();
//                               //   } catch (e) {
//                               //     debugPrint(e.toString());
//                               //   }
//                               // },
//                             ),
//                           );
//                         },
//                       ),
//                     );
//                   },
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

// class SettingsPermissionMultiSelectDropdown extends StatelessWidget {
//   final String title;
//   final List<String> options;
//   final List<String> selected;
//   final ValueChanged<List<String>> onChanged;

//   const SettingsPermissionMultiSelectDropdown({
//     super.key,
//     required this.title,
//     required this.options,
//     required this.selected,
//     required this.onChanged,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return InputDecorator(
//       decoration: InputDecoration(
//         labelText: title,
//         border: const OutlineInputBorder(),
//       ),
//       child: Wrap(
//         spacing: 8,
//         runSpacing: 8,
//         children: options
//             .map(
//               (option) => FilterChip(
//                 label: Text(option),
//                 selected: selected.contains(option),
//                 onSelected: (bool sel) {
//                   final newSelected = List<String>.from(selected);
//                   if (sel) {
//                     newSelected.add(option);
//                   } else {
//                     newSelected.remove(option);
//                   }
//                   onChanged(newSelected);
//                 },
//               ),
//             )
//             .toList(),
//       ),
//     );
//   }
// }
