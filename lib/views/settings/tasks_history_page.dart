import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:legacy_pms/models/masterqcmodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/firebase.dart';
import 'package:legacy_pms/models/taskmodel.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:pluto_grid/pluto_grid.dart';

class TasksHistoryPage extends StatefulWidget {
  const TasksHistoryPage({super.key});

  @override
  State<TasksHistoryPage> createState() => _TasksHistoryPageState();
}

class _TasksHistoryPageState extends State<TasksHistoryPage> {
  List<TaskModel> completedTasks = [];
  bool isLoading = true;
  late PlutoGridStateManager gridStateManager;
  DateTime? selectedDate;

  @override
  void initState() {
    super.initState();
    _loadCompletedTasks();
  }

  Future<void> _loadCompletedTasks() async {
    setState(() => isLoading = true);

    try {
      var query = FBFireStore.tasks
          .where('completed', isEqualTo: true)
          .where('status', isEqualTo: TaskStatus.completed);

      if (selectedDate != null) {
        final startOfDay = DateTime(
          selectedDate!.year,
          selectedDate!.month,
          selectedDate!.day,
        );
        final endOfDay = startOfDay.add(const Duration(days: 1));
        query = query
            .where('completedAt', isGreaterThanOrEqualTo: startOfDay)
            .where('completedAt', isLessThan: endOfDay);
      }

      final snapshot = await query.get();

      setState(() {
        completedTasks = snapshot.docs
            .map((doc) => TaskModel.fromSnap(doc))
            .toList();
        completedTasks.sort((a, b) => b.completedAt!.compareTo(a.completedAt!));
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
      showSnackBar('Failed to load completed tasks: $e');
      debugPrint("error: $e");
    }
  }

  void _onDateSelected(DateTime? date) {
    setState(() {
      selectedDate = date;
    });
    _loadCompletedTasks();
  }

  List<PlutoColumn> _buildColumns() {
    final ctrl = Get.find<HomeCtrl>();
    return [
      PlutoColumn(
        title: "Date",
        field: "createdAt",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Completed At",
        field: "completedAt",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Customer",
        field: "customer",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Activity Type",
        field: "pType",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Tasks",
        field: "details",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Employee",
        field: "employee",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "TL",
        field: "supervisor",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Status",
        field: "status",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Result",
        field: "result",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Hours",
        field: "hours",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Report Sent",
        field: "reportSentStatus",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Customer Call",
        field: "customerCallStatus",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "Call Summary",
        field: "callSummary",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
      PlutoColumn(
        title: "QC File",
        field: "qcFile",
        type: PlutoColumnType.text(),
        readOnly: true,
        renderer: (rendererContext) {
          final task = rendererContext.row.cells['actions']!.value as TaskModel;
          final qcId = task.qcFile;
          final qcTitle = getQcTitle(ctrl.masterQcs, qcId) ?? 'N/A';

          return InkWell(
            onTap: () async {
              final qc = ctrl.masterQcs.firstWhereOrNull(
                (m) => m.docId == qcId,
              );

              if (qc != null) {
                await showDialog(
                  context: context,
                  builder: (context) =>
                      QcHistoryViewDialog(ctrl: ctrl, masterQc: qc, task: task),
                );
              } else {
                showSnackBar('QC data not found');
              }
            },
            child: Text(
              qcTitle,
              style: TextStyle(
                color: qcId != null && qcId.isNotEmpty
                    ? Colors.blue
                    : Colors.grey,
                decoration: qcId != null && qcId.isNotEmpty
                    ? TextDecoration.underline
                    : TextDecoration.none,
              ),
            ),
          );
        },
      ),
      PlutoColumn(
        title: "Errors",
        field: "numOfErrors",
        type: PlutoColumnType.number(),
        readOnly: true,
        renderer: (rendererContext) {
          final val = rendererContext.cell.value;
          final numberVal = val is int
              ? val
              : int.tryParse(val.toString()) ?? 0;
          final color = numberVal > 0 ? Colors.red : Colors.black;
          return Text(
            val.toString(),
            style: TextStyle(
              color: color,
              fontWeight: numberVal > 0 ? FontWeight.bold : FontWeight.normal,
            ),
          );
        },
      ),
      PlutoColumn(
        title: "Links and Comments",
        field: "linksandcomments",
        type: PlutoColumnType.text(),
        readOnly: true,
      ),
    ];
  }

  String? getQcTitle(List<Masterqcmodel> qcs, String? id) {
    if (id == null) return null;
    final qc = qcs.firstWhereOrNull((item) => item.docId == id);
    return qc?.title;
  }

  List<PlutoRow> _buildRows() {
    return completedTasks.map((task) {
      final ctrl = Get.find<HomeCtrl>();

      final customer = ctrl.customers.firstWhereOrNull(
        (c) => c.docId == task.customer,
      );
      final employee = ctrl.users.firstWhereOrNull(
        (u) => u.docId == task.employee,
      );
      final supervisor = ctrl.users.firstWhereOrNull(
        (u) => u.docId == task.supervisor,
      );
      final activity = ctrl.masterTasks.firstWhereOrNull(
        (a) => a.docId == task.pType,
      );

      final qcFileName = getQcTitle(ctrl.masterQcs, task.qcFile) ?? '';

      return PlutoRow(
        cells: {
          'createdAt': PlutoCell(
            value: DateFormat('dd-MM-yyyy').format(task.createdAt),
          ),
          'completedAt': PlutoCell(
            value: DateFormat(
              'dd-MM-yyyy HH:mm',
            ).format(task.completedAt ?? task.createdAt),
          ),
          'customer': PlutoCell(value: customer?.name ?? ''),
          'pType': PlutoCell(value: activity?.title ?? ''),
          'details': PlutoCell(value: task.details),
          'employee': PlutoCell(value: employee?.name ?? ''),
          'supervisor': PlutoCell(value: supervisor?.name ?? ''),
          'status': PlutoCell(value: task.status ?? ''),
          'result': PlutoCell(value: task.result ?? ''),
          'hours': PlutoCell(value: task.actualHours ?? ''),
          'reportSentStatus': PlutoCell(value: task.reportSentStatus ?? ''),
          'customerCallStatus': PlutoCell(value: task.customerCallStatus ?? ''),
          'callSummary': PlutoCell(value: task.callSummary ?? ''),
          'qcFile': PlutoCell(value: qcFileName),
          'numOfErrors': PlutoCell(
            value: int.tryParse(task.numOfErrors ?? '0') ?? 0,
          ),
          'linksandcomments': PlutoCell(value: task.linksAndComments ?? ''),
          'actions': PlutoCell(value: task),
        },
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    // final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// Date Picker
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              // Text(
              //   'Select Date to filter tasks:',
              //   style: textTheme.titleMedium,
              // ),
              // const SizedBox(width: 20),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green, // Primary button color
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 14,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 3,
                  shadowColor: Colors.green.withValues(alpha: 0.5),
                ),
                onPressed: () async {
                  final picked = await showDatePicker(
                    context: context,
                    initialDate: selectedDate ?? DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime.now(),
                  );
                  if (picked != null) _onDateSelected(picked);
                },
                child: Text(
                  selectedDate == null
                      ? 'Select Date'
                      : DateFormat('dd-MM-yyyy').format(selectedDate!),
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              if (selectedDate != null)
                IconButton(
                  icon: const Icon(Icons.clear),
                  tooltip: 'Clear Date Filter',
                  onPressed: () {
                    _onDateSelected(null);
                  },
                ),
            ],
          ),
          const SizedBox(height: 20),

          /// Grid or loading or empty state
          Expanded(
            child: isLoading
                ? const Center(child: CircularProgressIndicator())
                : completedTasks.isEmpty
                ? Center(
                    child: Text(
                      'No completed tasks found',
                      style: TextStyle(
                        fontSize: 20,
                        color: Colors.grey.shade700,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  )
                : PlutoGrid(
                    columns: _buildColumns(),
                    rows: _buildRows(),
                    onLoaded: (event) {
                      gridStateManager = event.stateManager;
                    },
                    configuration: PlutoGridConfiguration(
                      style: PlutoGridStyleConfig(
                        borderColor: Colors.grey.shade300,
                        rowHeight: 45,
                        gridBorderColor: Colors.grey.shade200,
                      ),
                      scrollbar: PlutoGridScrollbarConfig(
                        draggableScrollbar: true,
                        isAlwaysShown: true,
                        scrollbarThickness:
                            PlutoScrollbar.defaultThicknessWhileDragging,
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}

class QcHistoryViewDialog extends StatefulWidget {
  final Masterqcmodel masterQc;
  final TaskModel task;
  final HomeCtrl ctrl;

  const QcHistoryViewDialog({
    super.key,
    required this.masterQc,
    required this.task,
    required this.ctrl,
  });

  @override
  State<QcHistoryViewDialog> createState() => _QcHistoryViewDialogState();
}

class _QcHistoryViewDialogState extends State<QcHistoryViewDialog> {
  Map<String, dynamic> _savedAnswers = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSavedAnswers();
  }

  Future<void> _loadSavedAnswers() async {
    try {
      final doc = await FBFireStore.tasks.doc(widget.task.docId).get();
      final data = doc.data();

      if (data != null && data['answers'] != null) {
        final Map<String, dynamic> answers = Map<String, dynamic>.from(
          data['answers'],
        );

        // Get answers from any user who submitted QC for this task
        for (final userUid in answers.keys) {
          final userAnswers = Map<String, dynamic>.from(answers[userUid] ?? {});
          if (userAnswers.isNotEmpty) {
            _savedAnswers = userAnswers;
            break; // Use the first available answers
          }
        }
      }

      setState(() => _isLoading = false);
    } catch (e) {
      debugPrint('❌ Error loading QC answers: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final inputs = widget.masterQc.qcInputModel ?? [];

    return AlertDialog(
      backgroundColor: popupBgColor,
      titlePadding: const EdgeInsets.fromLTRB(24, 20, 24, 10),
      contentPadding: const EdgeInsets.fromLTRB(24, 0, 24, 10),
      actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '${widget.masterQc.title.toUpperCase()} - VIEW ONLY',
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: _savedAnswers.isNotEmpty ? Colors.green : Colors.grey,
              shape: BoxShape.circle,
            ),
          ),
        ],
      ),
      content: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Container(
              width: MediaQuery.of(context).size.width * 0.8,
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.7,
              ),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    if (_savedAnswers.isEmpty)
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(bottom: 12),
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.grey,
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'No QC data submitted for this task',
                              style: TextStyle(
                                color: Colors.grey,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (inputs.isEmpty)
                      const Text(
                        'No QC inputs available.',
                        style: TextStyle(
                          color: Colors.grey,
                          fontStyle: FontStyle.italic,
                        ),
                      )
                    else
                      ...inputs.whereType<Map<String, dynamic>>().map(
                        (item) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: _buildViewField(item),
                        ),
                      ),
                  ],
                ),
              ),
            ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildViewField(Map<String, dynamic> item) {
    final title = item['title'] ?? '';
    final type = item['type'] ?? 'textfield';
    final key = title.toLowerCase().trim();
    final displayTitle = title.isNotEmpty
        ? title[0].toUpperCase() + title.substring(1)
        : '';

    final savedValue = _savedAnswers[key];

    if (type == 'yesno') {
      return Row(
        children: [
          Expanded(
            child: Text(
              displayTitle,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              savedValue == true ? 'Yes' : 'No',
              style: TextStyle(
                color: savedValue == true ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            displayTitle,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Text(
              savedValue?.toString() ?? 'No data',
              style: TextStyle(
                color: savedValue != null ? Colors.black : Colors.grey,
                fontStyle: savedValue != null
                    ? FontStyle.normal
                    : FontStyle.italic,
              ),
            ),
          ),
        ],
      );
    }
  }
}
