import 'dart:io';

import 'package:excel/excel.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:legacy_pms/models/rolemodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/firebase.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:path_provider/path_provider.dart';

class CreateRolePage extends StatefulWidget {
  const CreateRolePage({super.key});

  @override
  State<CreateRolePage> createState() => _CreateRolePageState();
}

class _CreateRolePageState extends State<CreateRolePage> {
  List<Rolemodel> roles = [];
  bool isLoading = true;
  bool isAdding = false;
  bool isEditing = false;
  Rolemodel? editingRole;
  bool isRoleExporting = false;

  // Form controllers
  final TextEditingController roleNameController = TextEditingController();
  List<String> selectedPermissions = [];

  final CollectionReference rolesCollection = FBFireStore.roles;

  @override
  void initState() {
    super.initState();
    _loadRoles();
  }

  Future<void> _loadRoles() async {
    setState(() {
      isLoading = true;
    });

    try {
      final snapshot = await rolesCollection.get();
      final loadedRoles = snapshot.docs
          .map((doc) => Rolemodel.fromSnap(doc))
          .toList();
      loadedRoles.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      setState(() {
        roles = loadedRoles;
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
      showSnackBar('Failed to load roles: $e');
    }
  }

  void _resetForm() {
    roleNameController.clear();
    selectedPermissions = [];
  }

  void _showAddDialog() {
    _resetForm();
    setState(() {
      isAdding = true;
      isEditing = false;
      editingRole = null;
    });

    showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => const RoleDialog(),
    ).then((added) {
      if (added == true) {
        _loadRoles();
      }
    });
  }

  void _showEditDialog(Rolemodel role) {
    setState(() {
      isEditing = true;
      isAdding = false;
      editingRole = role;
    });

    showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => RoleDialog(
        roleId: role.docId,
        initialTitle: role.title,
        initialPermissions: List<String>.from(role.permissions),
      ),
    ).then((updated) {
      if (updated == true) {
        _loadRoles();
      }
    });
  }

  Future<void> _deleteRole(Rolemodel role) async {
    await showDialog<bool>(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: Text('Are you sure you want to delete "${role.title}"?'),
        actions: [
          TextButton(onPressed: () => context.pop(), child: const Text('No')),
          ElevatedButton(
            onPressed: () async {
              try {
                await rolesCollection.doc(role.docId).delete();
                showSnackBar('Role deleted successfully');
                _loadRoles();
                context.pop(true);
              } catch (e) {
                debugPrint(e.toString());
                showSnackBar('Failed to delete role: $e');
                context.pop(false);
              }
            },

            // Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(),
            child: const Text('Yes'),
          ),
        ],
      ),
    );

    // if (confirmed == true) {
    //   try {
    //     await rolesCollection.doc(role.docId).delete();
    //     showSnackBar('Role deleted successfully');
    //     _loadRoles();
    //   } catch (e) {
    //     showSnackBar('Failed to delete role: $e');
    //   }
    // }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: logoTealColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: logoTealColor.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Role Management',
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Create and manage user roles and permissions',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
                isRoleExporting
                    ? CircularProgressIndicator()
                    : ElevatedButton.icon(
                        onPressed: () =>
                            exportRolesToExcel(roles: roles, context: context),
                        // _showAddDialog,
                        icon: const Icon(Icons.add),
                        label: const Text('Export'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: whiteColor,
                          foregroundColor: logoTealColor,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                const SizedBox(width: 10),
                ElevatedButton.icon(
                  onPressed: _showAddDialog,
                  // onPressed: () => RoleDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('Add Role'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: logoTealColor,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 16,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),
          if (isLoading)
            const Center(child: CircularProgressIndicator())
          else if (roles.isEmpty)
            _buildEmptyState()
          else
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: _buildRolesList(),
            ),
        ],
      ),
    );
  }

  Future<void> exportRolesToExcel({
    required List<Rolemodel> roles,
    required BuildContext context,
  }) async {
    setState(() {
      isRoleExporting = true;
    });
    try {
      final excel = Excel.createExcel();
      excel.delete('Sheet1');
      final sheet = excel['Roles'];

      // Header row
      sheet.appendRow([
        TextCellValue('Sr.No'),
        TextCellValue('Role Name'),
        TextCellValue('Permissions'),
      ]);

      for (var i = 0; i < roles.length; i++) {
        final role = roles[i];

        sheet.appendRow([
          TextCellValue('${i + 1}'),
          TextCellValue(role.title),
          TextCellValue(role.permissions.join(', ')),
        ]);
      }

      final excelBytes = excel.encode();
      final filename = 'Roles_${DateTime.now().millisecondsSinceEpoch}.xlsx';

      if (kIsWeb) {
        await FileSaver.instance.saveFile(
          name: filename,
          bytes: Uint8List.fromList(excelBytes!),
          fileExtension: 'xlsx',
        );
        showSnackBar('Exported roles to $filename');
        setState(() {
          isRoleExporting = false;
        });
      } else {
        final dir = await getExternalStorageDirectory();
        final filePath = '${dir!.path}/$filename';
        final file = File(filePath);
        await file.writeAsBytes(excelBytes!);
        showCtcAppSnackBar(context, 'Exported roles to $filePath');

        setState(() {
          isRoleExporting = false;
        });
      }
    } catch (e) {
      showSnackBar('Export failed: $e');
      setState(() {
        isRoleExporting = false;
      });
    }
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(48),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
        // border: Border.all(color: Colors.grey.shade200),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.admin_panel_settings,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No Roles Available',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first role to get started',
              style: TextStyle(color: Colors.grey.shade500),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRolesList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Existing Roles',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: logoTealColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          color: logoTealColor.withOpacity(0.1),
          child: Row(
            children: const [
              Expanded(flex: 1, child: Text("Sr.")),
              Expanded(flex: 3, child: Text("Role Name")),
              Expanded(flex: 5, child: Text("Permissions")),
              Expanded(flex: 3, child: Center(child: Text("Actions"))),
            ],
          ),
        ),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: roles.length,
          itemBuilder: (context, index) {
            final role = roles[index];
            return Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
              decoration: BoxDecoration(
                // border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Row(
                children: [
                  Expanded(flex: 1, child: Text("${index + 1}")),
                  Expanded(flex: 3, child: Text(role.title)),
                  Expanded(flex: 5, child: Text(role.permissions.join(', '))),
                  Expanded(
                    flex: 3,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit, size: 20),
                          color: logoTealColor,
                          onPressed: () => _showEditDialog(role),
                          tooltip: 'Edit Role',
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete, size: 20),
                          color: Colors.red,
                          onPressed: () => _deleteRole(role),
                          tooltip: 'Delete Role',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }
}

class RoleDialog extends StatefulWidget {
  final String? roleId; // null means 'Add', non-null means 'Edit'
  final String? initialTitle;
  final List<String>? initialPermissions;

  const RoleDialog({
    super.key,
    this.roleId,
    this.initialTitle,
    this.initialPermissions,
  });

  @override
  _RoleDialogState createState() => _RoleDialogState();
}

class _RoleDialogState extends State<RoleDialog> {
  late TextEditingController _titleController;
  late List<String> selectedPermissions;
  bool _isSubmitting = false;
  final CollectionReference rolesCollection = FBFireStore.roles;

  bool get isEditing => widget.roleId != null;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.initialTitle ?? '');
    selectedPermissions = List<String>.from(widget.initialPermissions ?? []);
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    final roleName = _titleController.text.trim();
    if (roleName.isEmpty) {
      showSnackBar('Please enter a role name.');

      return;
    }

    setState(() => _isSubmitting = true);

    try {
      if (isEditing) {
        // Update existing role
        await rolesCollection.doc(widget.roleId).update({
          'title': roleName,
          'permissions': selectedPermissions,
        });
        showSnackBar('Role updated successfully.');
      } else {
        // Add new role
        await rolesCollection.add({
          'title': roleName,
          'createdAt': FieldValue.serverTimestamp(),
          'permissions': selectedPermissions,
        });
        showSnackBar('Role added successfully.');
      }

      Navigator.of(context).pop(true); // indicate success
    } catch (e) {
      showSnackBar('Failed to save role: $e');
    } finally {
      setState(() => _isSubmitting = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: popupBgColor,
      insetPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
      content: SingleChildScrollView(
        padding: const EdgeInsets.all(12),
        child: SizedBox(
          width: 600,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isEditing ? 'Edit Role' : 'Add Role',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _titleController,
                autocorrect: false,
                textCapitalization: TextCapitalization.words,
                decoration: InputDecoration(
                  labelText: 'Role Name',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                ),
              ),
              const SizedBox(height: 20),
              SettingsPermissionMultiSelectDropdown(
                title: "Permissions",
                options: Permissions.all,
                selected: selectedPermissions,
                onChanged: (newList) =>
                    setState(() => selectedPermissions = newList),
              ),
              const SizedBox(height: 30),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _isSubmitting
                        ? null
                        : () => Navigator.of(context).pop(false),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _isSubmitting ? null : _submit,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: logoTealColor,
                      foregroundColor: whiteColor,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 28,
                        vertical: 13,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      textStyle: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    child: _isSubmitting
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2.5,
                            ),
                          )
                        : Text(
                            isEditing ? 'Update' : 'Add',
                            style: const TextStyle(color: Colors.white),
                          ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Stateful multi-select chip widget
class SettingsPermissionMultiSelectDropdown extends StatefulWidget {
  final String title;
  final List<String> options;
  final List<String> selected;
  final ValueChanged<List<String>> onChanged;

  const SettingsPermissionMultiSelectDropdown({
    super.key,
    required this.title,
    required this.options,
    required this.selected,
    required this.onChanged,
  });

  @override
  State<SettingsPermissionMultiSelectDropdown> createState() =>
      _SettingsPermissionMultiSelectDropdownState();
}

class _SettingsPermissionMultiSelectDropdownState
    extends State<SettingsPermissionMultiSelectDropdown> {
  @override
  Widget build(BuildContext context) {
    return InputDecorator(
      decoration: InputDecoration(
        labelText: widget.title,
        border: const OutlineInputBorder(
          borderRadius: BorderRadius.zero, // edgy corners
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 10, bottom: 10),
        child: Wrap(
          spacing: 8,
          runSpacing: 8,
          children: widget.options.map((option) {
            return FilterChip(
              shape: const ContinuousRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(14)),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              label: Text(option),
              selected: widget.selected.contains(option),
              onSelected: (bool selectedFlag) {
                final newSelected = List<String>.from(widget.selected);
                if (selectedFlag) {
                  newSelected.add(option);
                } else {
                  newSelected.remove(option);
                }
                widget.onChanged(newSelected);
              },
            );
          }).toList(),
        ),
      ),
    );
  }
}
