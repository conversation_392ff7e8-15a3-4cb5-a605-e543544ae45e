import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/router.dart';

class DashboardTile extends StatelessWidget {
  const DashboardTile({
    super.key,
    this.icon,
    required this.textName,
    this.isSelected = false,
    this.tab = false,
    this.scafKey,
    required this.route,
    this.number,
    this.showNumbers,
  });

  final IconData? icon;
  final String textName;
  final bool isSelected;
  final bool tab;
  final GlobalKey<ScaffoldState>? scafKey;
  final String route;
  final String? number;
  final bool? showNumbers;

  @override
  Widget build(BuildContext context) {
    final selected = appRouter.routeInformationProvider.value.uri.path == route;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () {
          context.go(route);
          Get.find<HomeCtrl>().update();
          // scafKey?.currentState?.closeDrawer();
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          // No background anymore
          child: tab ? _column(selected) : _row(selected),
        ),
      ),
    );
  }

  /// Row layout (for desktop / wide screens)
  Row _row(bool selected) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (icon != null) ...[
          // Icon(
          //   icon,
          //   size: 20,
          //   color: selected ? Colors.green.shade700 : Colors.black,
          // ),
          const SizedBox(width: 8),
        ],

        /// Text + Underline stacked vertically
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              textName,
              style: TextStyle(
                fontSize: 14,
                fontWeight: selected ? FontWeight.w600 : FontWeight.w400,
                color: selected ? orangeColor : Colors.black,
              ),
            ),
            const SizedBox(height: 3),
            AnimatedContainer(
              duration: const Duration(milliseconds: 250),
              curve: Curves.easeInOut,
              height: 2,
              width: selected ? 24 : 0,
              decoration: BoxDecoration(
                color: selected ? logoTealColor : Colors.transparent,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),

        if ((showNumbers ?? false) && (number != "0")) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Color(0xFFEE8023),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              number ?? "",
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Column layout (for tablets / bottom nav style)
  Column _column(bool selected) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (icon != null) ...[
          // Icon(
          //   icon,
          //   size: 22,
          //   color: selected ? Colors.green.shade700 : Colors.black,
          // ),
          const SizedBox(height: 4),
        ],
        Text(
          textName,
          style: TextStyle(
            fontSize: 12,
            fontWeight: selected ? FontWeight.w600 : FontWeight.w400,
            color: selected ? orangeColor : Colors.black,
          ),
        ),
        const SizedBox(height: 3),
        AnimatedContainer(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeInOut,
          height: 2,
          width: selected ? 20 : 0,
          decoration: BoxDecoration(
            color: selected ? logoTealColor : Colors.transparent,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ],
    );
  }
}
