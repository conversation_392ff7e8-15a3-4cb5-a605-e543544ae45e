import 'package:flutter/material.dart';
import 'package:legacy_pms/models/notificationmodel.dart';
import 'package:legacy_pms/shared/firebase.dart';

class NotificationListWidget extends StatefulWidget {
  final VoidCallback onClose;

  const NotificationListWidget({super.key, required this.onClose});

  @override
  State<NotificationListWidget> createState() => _NotificationListWidgetState();
}

class _NotificationListWidgetState extends State<NotificationListWidget> {
  List<Notificationmodel> notifications = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    try {
      // For demo purposes, let's add some dummy data
      final dummyNotifications = [
        Notificationmodel(
          docId: '1',
          title: 'New Task Assigned',
          desc:
              'You have been assigned a new task: Complete quarterly report for ABC Corp. Please review the requirements and start working on it.',
          createdAt: DateTime.now().subtract(Duration(minutes: 5)),
          topic: '',
          test: false,
        ),
        Notificationmodel(
          docId: '2',
          title: 'Task Deadline Approaching',
          desc:
              'Your task "Website redesign for XYZ Company" is due in 2 hours. Please ensure completion.',
          createdAt: DateTime.now().subtract(Duration(hours: 1)),
          topic: '',
          test: false,
        ),
        Notificationmodel(
          docId: '3',
          title: 'Meeting Reminder',
          desc:
              'Team standup meeting starts in 15 minutes in Conference Room A.',
          createdAt: DateTime.now().subtract(Duration(hours: 3)),
          topic: '',
          test: false,
        ),
        Notificationmodel(
          docId: '4',
          title: 'Customer Feedback Received',
          desc:
              'New feedback received from Johnson & Associates regarding the recent project delivery.',
          createdAt: DateTime.now().subtract(Duration(days: 1)),
          topic: '',
          test: false,
        ),
        Notificationmodel(
          docId: '5',
          title: 'System Maintenance',
          desc:
              'Scheduled system maintenance will occur tonight from 11 PM to 2 AM. Please save your work.',
          createdAt: DateTime.now().subtract(Duration(days: 2)),
          topic: '',
          test: false,
        ),
      ];

      // Try to load from Firestore, fallback to dummy data
      try {
        final snapshot = await FBFireStore.notifications
            .orderBy('createdAt', descending: true)
            .limit(10)
            .get();

        if (snapshot.docs.isNotEmpty) {
          setState(() {
            notifications = snapshot.docs
                .map((doc) => Notificationmodel.fromSnap(doc))
                .toList();
            isLoading = false;
          });
        } else {
          // Use dummy data if no real notifications
          setState(() {
            notifications = dummyNotifications;
            isLoading = false;
          });
        }
      } catch (e) {
        // Use dummy data on error
        setState(() {
          notifications = dummyNotifications;
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with notification count
        Row(
          children: [
            Icon(Icons.notifications, color: Colors.deepPurple, size: 24),
            const SizedBox(width: 12),
            Text(
              "Notifications",
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 18,
                color: Colors.black87,
              ),
            ),
            if (notifications.isNotEmpty) ...[
              const SizedBox(width: 8),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.deepPurple,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '${notifications.length}',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
            Spacer(),
            IconButton(
              onPressed: widget.onClose,
              icon: Icon(Icons.close, size: 20),
              splashRadius: 16,
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Content
        if (isLoading)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: CircularProgressIndicator(color: Colors.deepPurple),
            ),
          )
        else if (notifications.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Column(
                children: [
                  Icon(
                    Icons.notifications_none,
                    size: 48,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    "No notifications",
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 16),
                  ),
                ],
              ),
            ),
          )
        else
          Flexible(
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: notifications.length,
              separatorBuilder: (_, __) =>
                  Divider(height: 1, color: Colors.grey.shade200),
              itemBuilder: (context, index) {
                final notification = notifications[index];
                final timeAgo = _getTimeAgo(notification.createdAt);

                return InkWell(
                  onTap: () {
                    // Handle notification tap - could navigate to specific page
                    print('Tapped notification: ${notification.title}');
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Notification indicator
                        Container(
                          margin: EdgeInsets.only(top: 6),
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: index < 3
                                ? Colors.deepPurple
                                : Colors.grey.shade400,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 12),
                        // Content
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                notification.title,
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                  color: Colors.black87,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                notification.desc,
                                style: TextStyle(
                                  fontSize: 13,
                                  color: Colors.black54,
                                  height: 1.3,
                                ),
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 6),
                              Text(
                                timeAgo,
                                style: TextStyle(
                                  fontSize: 11,
                                  color: Colors.grey.shade500,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),

        // Footer
        if (notifications.isNotEmpty) ...[
          // Divider(color: Colors.grey.shade200),
          // Center(
          //   child: TextButton.icon(
          //     onPressed: () {
          //       widget.onClose();
          //       // Navigate to full notifications page
          //       print('Navigate to all notifications');
          //     },
          //     icon: Icon(Icons.arrow_forward, size: 16),
          //     label: Text("View All Notifications"),
          //     style: TextButton.styleFrom(
          //       foregroundColor: Colors.deepPurple,
          //       textStyle: TextStyle(fontWeight: FontWeight.w600),
          //     ),
          //   ),
          // ),
        ],
      ],
    );
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
