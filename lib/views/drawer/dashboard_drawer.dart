import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:legacy_pms/models/notificationmodel.dart';
import 'package:legacy_pms/models/usermodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:legacy_pms/views/drawer/notification.dart';
import '../../controller/homectrl.dart';
import '../../shared/router.dart';
import '../../shared/firebase.dart';
import 'db_drawer_tile.dart';

class DashboardDrawer extends StatefulWidget {
  const DashboardDrawer({super.key, this.isTablet = false, this.scafKey});

  final bool isTablet;
  final GlobalKey<ScaffoldState>? scafKey;

  @override
  State<DashboardDrawer> createState() => _DashboardDrawerState();
}

final _bellKey = GlobalKey();

class _DashboardDrawerState extends State<DashboardDrawer> {
  bool loading = false;
  bool processingPunch = false;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      child: GetBuilder<HomeCtrl>(
        builder: (ctrl) {
          final userEmail = FBAuth.auth.currentUser?.email;

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            height: 70,
            width: double.infinity,
            decoration: const BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const DashHeader(),
                const SizedBox(width: 24),
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        if (ctrl.userRoles.contains(
                          Permissions.canSeeDashboardTab,
                        ))
                          DashboardTile(
                            textName: 'Dashboard',
                            tab: widget.isTablet,
                            scafKey: widget.scafKey,
                            route: Routes.dashboard,
                          ),
                        if (ctrl.userRoles.contains(Permissions.canSeeTasksTab))
                          DashboardTile(
                            textName: 'Worksheet',
                            tab: widget.isTablet,
                            scafKey: widget.scafKey,
                            route: Routes.tasks,
                          ),
                        if (ctrl.userRoles.contains(
                          Permissions.canSeeAttendanceDashboard,
                        ))
                          DashboardTile(
                            textName: 'Attendance',
                            tab: widget.isTablet,
                            scafKey: widget.scafKey,
                            route: Routes.attendance,
                          ),
                        if (ctrl.userRoles.contains(Permissions.canSeeUsersTab))
                          DashboardTile(
                            textName: 'Users',
                            tab: widget.isTablet,
                            scafKey: widget.scafKey,
                            route: Routes.users,
                          ),
                        if (ctrl.userRoles.contains(
                          Permissions.canSeeCustomersTab,
                        ))
                          DashboardTile(
                            textName: 'Customers',
                            tab: widget.isTablet,
                            scafKey: widget.scafKey,
                            route: Routes.customers,
                          ),
                        if (ctrl.userRoles.contains(
                          Permissions.canSeeSettingsTab,
                        ))
                          DashboardTile(
                            textName: 'Settings',
                            tab: widget.isTablet,
                            scafKey: widget.scafKey,
                            route: Routes.settings,
                          ),
                      ],
                    ),
                  ),
                ),
                if (ctrl.userlatestPunch != null)
                  Text(
                    style: TextStyle(
                      fontSize: 15.5,
                      fontWeight: FontWeight.w500,
                    ),
                    DateFormat(
                      'HH:mm a',
                    ).format(ctrl.userlatestPunch?.createdAt ?? DateTime.now()),
                  ),
                SizedBox(width: 4),

                //bell Icon
                IconButton(
                  padding: EdgeInsets.only(),
                  key: _bellKey,
                  icon: Icon(Icons.notifications_none_outlined),
                  onPressed: () {
                    final overlay = Overlay.of(context);
                    final renderBox =
                        _bellKey.currentContext!.findRenderObject()
                            as RenderBox;
                    final position = renderBox.localToGlobal(Offset.zero);
                    final size = renderBox.size;

                    late OverlayEntry overlayEntry;

                    overlayEntry = OverlayEntry(
                      builder: (context) => Stack(
                        children: [
                          // Backdrop to detect clicks outside
                          GestureDetector(
                            onTap: () => overlayEntry.remove(),
                            child: Container(
                              width: MediaQuery.of(context).size.width,
                              height: MediaQuery.of(context).size.height,
                              color: Colors.transparent,
                            ),
                          ),
                          // Notification popup
                          Positioned(
                            right:
                                MediaQuery.of(context).size.width -
                                position.dx -
                                size.width,
                            top: position.dy + size.height + 8,
                            child: Material(
                              elevation: 8,
                              borderRadius: BorderRadius.circular(12),
                              shadowColor: Colors.black26,
                              child: Container(
                                width: MediaQuery.of(context).size.width > 600
                                    ? 400
                                    : MediaQuery.of(context).size.width * 0.9,
                                constraints: BoxConstraints(
                                  maxHeight:
                                      MediaQuery.of(context).size.height * 0.6,
                                  minHeight: 150,
                                  maxWidth: 450,
                                  minWidth: 300,
                                ),
                                padding: EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.grey.shade200,
                                  ),
                                ),
                                child: NotificationListWidget(
                                  onClose: () => overlayEntry.remove(),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );

                    overlay.insert(overlayEntry);
                  },
                ),
                // Punch In Button
                // ctrl.loggedInUser?.role != admin
                //     ? ctrl.puchLoading
                //           ? const Padding(
                //               padding: EdgeInsets.only(right: 12),
                //               child: CircularProgressIndicator(),
                //             )
                //           : (((ctrl.settings?.requestHour ?? 19) <=
                //                         DateTime.now().hour &&
                //                     (ctrl.settings?.requestMin ?? 0) <=
                //                         DateTime.now().minute) ||
                //                 !ctrl.todaysPunch)
                //           ? Padding(
                //               padding: const EdgeInsets.only(right: 12),
                //               child: IgnorePointer(
                //                 ignoring:
                //                     ctrl.requestsPunchOut.firstWhereOrNull(
                //                       (r) => r.uId == ctrl.loggedInUser?.docId,
                //                     ) !=
                //                     null,
                //                 child: ElevatedButton(
                //                   onPressed: () async {
                //                     await onRequestPunched(ctrl);
                //                   },
                //                   style: ElevatedButton.styleFrom(
                //                     backgroundColor:
                //                         ctrl.requestsPunchOut.firstWhereOrNull(
                //                               (r) =>
                //                                   r.uId ==
                //                                   ctrl.loggedInUser?.docId,
                //                             ) !=
                //                             null
                //                         ? Colors.grey
                //                         : Colors.red,
                //                     padding: const EdgeInsets.symmetric(
                //                       horizontal: 16,
                //                     ),
                //                     shape: RoundedRectangleBorder(
                //                       borderRadius: BorderRadius.circular(8),
                //                     ),
                //                   ),
                //                   child: Text(
                //                     "Request Punch Out",
                //                     style: TextStyle(color: Colors.white),
                //                   ),
                //                 ),
                //               ),
                //             )
                //           : Padding(
                //               padding: const EdgeInsets.only(right: 12),
                //               child: IgnorePointer(
                //                 ignoring:
                //                     ctrl.requestsPunchOut.firstWhereOrNull(
                //                       (r) => r.uId == ctrl.loggedInUser?.docId,
                //                     ) !=
                //                     null,
                //                 child: ElevatedButton(
                //                   onPressed: () async {
                //                     await onPunched(
                //                       ctrl,
                //                       ctrl.userlatestPunch == null
                //                           ? false
                //                           : ctrl.userlatestPunch!.punchIn,
                //                     );
                //                   },
                //                   style: ElevatedButton.styleFrom(
                //                     backgroundColor:
                //                         ctrl.requestsPunchOut.firstWhereOrNull(
                //                               (r) =>
                //                                   r.uId ==
                //                                   ctrl.loggedInUser?.docId,
                //                             ) !=
                //                             null
                //                         ? Colors.grey
                //                         : ctrl.userlatestPunch == null
                //                         ? Colors.green
                //                         : ctrl.userlatestPunch!.punchIn
                //                         ? Colors.red
                //                         : Colors.green,
                //                     padding: const EdgeInsets.symmetric(
                //                       horizontal: 16,
                //                     ),
                //                     shape: RoundedRectangleBorder(
                //                       borderRadius: BorderRadius.circular(8),
                //                     ),
                //                   ),
                //                   child: Text(
                //                     (ctrl.userlatestPunch == null)
                //                         ? "Punch In"
                //                         : ctrl.userlatestPunch!.punchIn
                //                         ? "Punch Out"
                //                         : "Punch In",
                //                     style: TextStyle(color: Colors.white),
                //                   ),
                //                 ),
                //               ),
                //             )
                //     : SizedBox.shrink(),

                // User Email Popup Menu
                PopupMenuButton<int>(
                  tooltip: '',
                  color: Colors.white,
                  onSelected: (value) {
                    if (value == 1) {
                      final user = ctrl.loggedInUser;
                      if (user != null) {
                        context.push('/userdetails/${user.docId}');
                      }
                    }
                    if (value == 2) {
                      _showLogoutDialog(context);
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 1,
                      child: Row(
                        children: const [
                          Icon(
                            CupertinoIcons.person,
                            color: Colors.black,
                            size: 20,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Profile',
                            style: TextStyle(color: Colors.black),
                          ),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 2,
                      child: Row(
                        children: const [
                          Icon(
                            CupertinoIcons.power,
                            color: Colors.red,
                            size: 20,
                          ),
                          SizedBox(width: 8),
                          Text('Logout', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 12),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xfff6f7fb),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.person, color: Colors.grey[700], size: 18),
                        const SizedBox(width: 6),
                        Text(
                          userEmail ?? "",
                          style: const TextStyle(
                            color: Colors.black87,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(Icons.keyboard_arrow_down, color: Colors.grey),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // Future<void> onRequestPunched(HomeCtrl ctrl) async {
  //   bool inProcessing = false;
  //   //CALCULATING TOTAL HOURS
  //   showDialog(
  //     context: context,
  //     builder: (context) {
  //       TimeOfDay? time = TimeOfDay.now();
  //       return StatefulBuilder(
  //         builder: (context, reBuild) {
  //           return AlertDialog(
  //             backgroundColor: Colors.white,
  //             title: Text("Request Punch Out!!"),
  //             content: InkWell(
  //               onTap: () async {
  //                 time =
  //                     await showTimePicker(
  //                       context: context,
  //                       initialTime: TimeOfDay.now(),
  //                     ) ??
  //                     TimeOfDay.now();
  //                 reBuild(() {});
  //               },
  //               child: Padding(
  //                 padding: const EdgeInsets.symmetric(vertical: 15.0),
  //                 child: Row(
  //                   mainAxisAlignment: MainAxisAlignment.center,
  //                   children: [
  //                     Text(
  //                       "Requested Time: ${time?.format(context) ?? ""}",
  //                       style: GoogleFonts.mulish(
  //                         fontSize: 16,
  //                         fontWeight: FontWeight.w500,
  //                       ),
  //                     ),
  //                     SizedBox(width: 5),
  //                     Icon(CupertinoIcons.clock),
  //                   ],
  //                 ),
  //               ),
  //             ),
  //             actions: [
  //               if (!inProcessing) ...[
  //                 OutlinedButton(
  //                   onPressed: inProcessing
  //                       ? () {}
  //                       : () async {
  //                           inProcessing = true;
  //                           reBuild(() {});
  //                           final data = {
  //                             'uId': FBAuth.auth.currentUser?.uid,
  //                             'createdAt': Timestamp.now(),
  //                             'requestTime': DateTime(
  //                               ctrl.userlatestPunch!.createdAt.year,
  //                               ctrl.userlatestPunch!.createdAt.month,
  //                               ctrl.userlatestPunch!.createdAt.day,
  //                               time?.hour ?? 0,
  //                               time?.minute ?? 0,
  //                             ),
  //                             'active': true,
  //                           };
  //                           await FBFireStore.requestPunchOut.add(data);
  //                           inProcessing = true;
  //                           reBuild(() {});
  //                           Navigator.pop(context);
  //                         },
  //                   child: Text("Request"),
  //                 ),
  //                 OutlinedButton(
  //                   onPressed: () {
  //                     Navigator.pop(context);
  //                   },
  //                   child: Text("Cancel"),
  //                 ),
  //               ],
  //               if (inProcessing) CircularProgressIndicator(),
  //             ],
  //           );
  //         },
  //       );
  //     },
  //   );
  // }

  Future<void> onPunched(HomeCtrl ctrl, bool pucnhedIn, String uId) async {
    // processingPunch = true;
    // setState(() {});

    final now = DateTime.now();
    final requestHour = ctrl.settings?.requestHour ?? 19;
    final requestMin = ctrl.settings?.requestMin ?? 0;

    if (!ctrl.todaysPunch ||
        (now.hour >= requestHour && now.minute >= requestMin)) {
      showCtcAppSnackBar(context, 'Time up for punch out. Please request.');
      setState(() {});
      return;
    }
    await punchOut(ctrl, uId);
  }

  void _showLogoutDialog(BuildContext context) {
    bool isLoading = false;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            // final accentColor = Colors.deepPurple;

            final cancelButtonStyle = TextButton.styleFrom(
              foregroundColor: Colors.grey[700],
              padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(14),
                side: BorderSide(color: Colors.grey.shade300),
              ),
              textStyle: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            );

            final logoutButtonStyle = ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(14),
              ),
              textStyle: const TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 16,
              ),
              elevation: 8,
              shadowColor: Colors.white,
            );

            return AlertDialog(
              backgroundColor: popupBgColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
              elevation: 24,
              titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
              contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 16),
              actionsPadding: const EdgeInsets.fromLTRB(20, 12, 20, 20),
              title: Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    padding: const EdgeInsets.all(8),
                    child: Icon(
                      Icons.logout,
                      size: 28,
                      color: Colors.deepPurple,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Text(
                    'Confirm Logout',
                    style: TextStyle(
                      fontWeight: FontWeight.w800,
                      fontSize: 24,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              content: const Text(
                'Are you sure you want to log out of your account?',
                style: TextStyle(
                  fontSize: 17,
                  height: 1.5,
                  color: Colors.black54,
                ),
              ),
              actions: [
                TextButton(
                  onPressed: isLoading
                      ? null
                      : () => Navigator.of(dialogContext).pop(),
                  style: cancelButtonStyle,
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isLoading
                      ? null
                      : () async {
                          setState(() => isLoading = true);
                          Usermodel? user = Get.find<HomeCtrl>().loggedInUser;

                          await FBAuth.auth.signOut();
                          if (user?.role != 'HR , Admin') {
                            onPunched(
                              Get.find<HomeCtrl>(),
                              true,
                              user?.docId ?? '',
                            );
                          }

                          Navigator.of(dialogContext).pop();
                          if (context.mounted) {
                            context.go(Routes.signin);
                          }
                        },
                  style: logoutButtonStyle,
                  child: isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2.5,
                          ),
                        )
                      : const Text('Logout'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}

/// Logo/Header (auto-fits)
class DashHeader extends StatelessWidget {
  const DashHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final isWide = MediaQuery.of(context).size.width > 600;
    return SizedBox(
      width: isWide ? 180 : 120,
      height: 50,
      child: Image.asset('assets/LegacyLogoLandscape.png', fit: BoxFit.contain),
    );
  }
}
