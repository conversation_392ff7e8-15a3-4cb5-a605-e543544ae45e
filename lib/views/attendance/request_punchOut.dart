import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'package:legacy_pms/shared/firebase.dart';
import 'package:legacy_pms/shared/methods.dart';
import '../../models/requestmodel.dart';

class RequestPunchOutPage extends StatefulWidget {
  const RequestPunchOutPage({Key? key}) : super(key: key);

  @override
  State<RequestPunchOutPage> createState() => _RequestPunchOutPageState();
}

class _RequestPunchOutPageState extends State<RequestPunchOutPage> {
  List<Requestmodel> requests = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    loadRequests();
  }

  Future<void> loadRequests() async {
    try {
      setState(() {
        isLoading = true;
      });

      final snapshot = await FBFireStore.requestPunchOut
          .where('active', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      setState(() {
        requests = snapshot.docs
            .map((doc) => Requestmodel.fromSnap(doc))
            .toList();
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      showCtcAppSnackBar(context, 'Error loading requests: $e');
      debugPrint(e.toString());
    }
  }

  Future<void> _acceptRequest(Requestmodel request) async {
    try {
      // Update the request to inactive
      await FBFireStore.requestPunchOut.doc(request.docId).update({
        'active': false,
        'status': 'accepted',
        'processedAt': Timestamp.fromDate(DateTime.now()),
      });

      // Add punch out record for the user
      await FBFireStore.punches.add({
        'uId': request.uId,
        'punchTime': request.requestTime,
        'type': 'punchOut',
        'createdAt': Timestamp.fromDate(DateTime.now()),
      });

      setState(() {
        requests.removeWhere((r) => r.docId == request.docId);
      });

      showCtcAppSnackBar(context, 'Request accepted successfully');
    } catch (e) {
      showCtcAppSnackBar(context, 'Error accepting request: $e');
    }
  }

  Future<void> _rejectRequest(Requestmodel request) async {
    try {
      await FBFireStore.requestPunchOut.doc(request.docId).update({
        'active': false,
        'status': 'rejected',
        'processedAt': Timestamp.fromDate(DateTime.now()),
      });

      setState(() {
        requests.removeWhere((r) => r.docId == request.docId);
      });

      showCtcAppSnackBar(context, 'Request rejected');
    } catch (e) {
      showCtcAppSnackBar(context, 'Error rejecting request: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      // backgroundColor: Colors.grey[100],
      child: isLoading
          ? const Center(child: CircularProgressIndicator())
          : requests.isEmpty
          ? _buildEmptyState()
          : _buildRequestsList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Card(
        color: Colors.white,
        elevation: 3,
        margin: const EdgeInsets.symmetric(horizontal: 40),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(28),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.inbox_outlined, size: 64, color: Colors.grey),
              const SizedBox(height: 24),
              Text(
                'No pending requests',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.grey[800],
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'All punch out requests have been processed',
                style: TextStyle(color: Colors.grey[600], fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRequestsList() {
    return RefreshIndicator(
      onRefresh: loadRequests,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: requests.length,
        itemBuilder: (context, index) {
          final request = requests[index];
          return _buildRequestCard(request);
        },
      ),
    );
  }

  Widget _buildRequestCard(Requestmodel request) {
    return Card(
      color: Colors.white,
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1976D2).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.logout,
                    color: Color(0xFF1976D2),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Punch Out Request',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'User ID: ${request.uId}',
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Pending',
                    style: TextStyle(
                      color: Colors.orange,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              'Request Time',
              DateFormat('dd MMM yyyy, hh:mm a').format(request.requestTime),
              Icons.access_time,
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              'Requested At',
              DateFormat('dd MMM yyyy, hh:mm a').format(request.createdAt),
              Icons.schedule,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Spacer(flex: 3),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _acceptRequest(request),
                    icon: const Icon(Icons.check, size: 18),
                    label: const Text('Accept'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _rejectRequest(request),
                    icon: const Icon(Icons.close, size: 18),
                    label: const Text('Reject'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
