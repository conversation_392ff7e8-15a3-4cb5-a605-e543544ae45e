import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/models/punchmodel.dart';
import 'package:legacy_pms/models/recordsmodel.dart';
import 'package:legacy_pms/models/usermodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:legacy_pms/views/attendance/request_punchOut.dart';

class Presentees {
  Usermodel user;
  List<Punchmodel> punchedn;
  int minutesLeft;
  Recordsmodel? userrecord;

  Presentees({
    required this.punchedn,
    required this.user,
    required this.minutesLeft,
    required this.userrecord,
  });
}

class AttendanceDashboard extends StatefulWidget {
  const AttendanceDashboard({super.key});

  @override
  State<AttendanceDashboard> createState() => _AttendanceDashboardState();
}

class _AttendanceDashboardState extends State<AttendanceDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    Timer.periodic(const Duration(minutes: 1), (timer) {
      if (mounted) setState(() {});
    });
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  bool _isTodayHolidayOrWeekendOrLeave(Usermodel user, Recordsmodel? record) {
    final now = DateTime.now();
    // Check if today is Sunday
    if (now.weekday == DateTime.sunday) return true;
    // Check if today is a holiday
    if (holidaylist.any(
      (h) => h.year == now.year && h.month == now.month && h.day == now.day,
    )) {
      return true;
    }
    // Check if today is a leave for the user
    if (record != null && record.leavedates != null) {
      String todayStr =
          "${now.day.toString().padLeft(2, '0')}/${now.month.toString().padLeft(2, '0')}/${now.year}";
      if (record.leavedates!.contains(todayStr)) return true;
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (hctrl) {
        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.08),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TabBar(
                  dividerHeight: 0,
                  dividerColor: Colors.transparent,
                  controller: _tabController,
                  indicatorColor: logoTealColor,
                  labelColor: logoTealColor,
                  unselectedLabelColor: Colors.grey.shade600,
                  labelStyle: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  isScrollable: false,
                  tabs: const [
                    Tab(icon: Icon(Icons.dashboard), text: "Dashboard"),
                    Tab(icon: Icon(Icons.request_page), text: "Requests"),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              Expanded(
                child: TabBarView(
                  physics: const NeverScrollableScrollPhysics(),
                  controller: _tabController,
                  children: [
                    AttendanceDashboardContent(
                      hctrl: hctrl,
                      isTodayHolidayOrWeekendOrLeave:
                          _isTodayHolidayOrWeekendOrLeave,
                    ),
                    RequestPunchOutPage(),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// Separated out AttendanceDashboardContent Widget
class AttendanceDashboardContent extends StatefulWidget {
  final HomeCtrl hctrl;
  final bool Function(Usermodel, Recordsmodel?) isTodayHolidayOrWeekendOrLeave;

  const AttendanceDashboardContent({
    super.key,
    required this.hctrl,
    required this.isTodayHolidayOrWeekendOrLeave,
  });

  @override
  State<AttendanceDashboardContent> createState() =>
      _AttendanceDashboardContentState();
}

class _AttendanceDashboardContentState
    extends State<AttendanceDashboardContent> {
  List<Presentees> presentees = [];

  List<Presentees> absentees = [];

  List<String> onLeaveUserID = [];
  bool isLoading = true;
  @override
  void initState() {
    super.initState();
    calculateData();
  }

  void calculateData() async {
    int i = 0;
    while (widget.hctrl.attendeeLoading && i < 10) {
      i++;
      await Future.delayed(const Duration(seconds: 1));
    }
    for (var user in widget.hctrl.users) {
      final userData = widget.hctrl.userTodaydata.firstWhereOrNull(
        (element) => element.user.docId == user.docId,
      );
      final userPunches = userData?.userTodayPunches ?? [];

      // Only today's punches
      userPunches.sort((a, b) => a.createdAt.compareTo(b.createdAt));
      final punchList = userPunches
          .where(
            (element) =>
                (element.createdAt.year == DateTime.now().year) &&
                (element.createdAt.month == DateTime.now().month) &&
                (element.createdAt.day == DateTime.now().day),
          )
          .toList();

      int minutesLeft = calculateMins(punchList);
      // If last punch is punchIn, add time since last punchIn
      if (punchList.isNotEmpty && punchList.last.punchIn) {
        minutesLeft += DateTime.now()
            .difference(punchList.last.createdAt)
            .inMinutes;
      }

      Recordsmodel? records = userData?.userRecord;

      // Filter logic for presentees and absentees
      if (punchList.isNotEmpty) {
        // User has at least one punch today, so present
        presentees.add(
          Presentees(
            punchedn: punchList,
            user: user,
            minutesLeft: minutesLeft,
            userrecord: records,
          ),
        );
      } else {
        // User has no punches today
        if (widget.isTodayHolidayOrWeekendOrLeave(user, records)) {
          // User is on leave/holiday/weekend, count as onLeave but not absent
          onLeaveUserID.add(user.docId);
        } else {
          // User is truly absent
          absentees.add(
            Presentees(
              punchedn: punchList,
              user: user,
              minutesLeft: minutesLeft,
              userrecord: records,
            ),
          );
          onLeaveUserID.add(user.docId); // Still count for header
        }
      }
    }

    // Sort presentees by first punch-in time
    presentees.sort(
      (a, b) => (a.punchedn.isNotEmpty && b.punchedn.isNotEmpty)
          ? (a.punchedn.first.createdAt).compareTo(b.punchedn.first.createdAt)
          : 0,
    );
    isLoading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    // Dashboard header
    Widget dashboardHeader() {
      return Padding(
        padding: const EdgeInsets.only(bottom: 32.0),
        child: Row(
          children: [
            Expanded(
              child: _InfoCard(
                icon: Icons.group,
                label: "Total Staff",
                value: "${widget.hctrl.users.length}",
                color: const Color(0xffcdc3ff),
              ),
            ),
            const SizedBox(width: 24),
            Expanded(
              child: _InfoCard(
                icon: Icons.work_off_outlined,
                label: "Leave/Absent",
                value: "${onLeaveUserID.length}",
                color: const Color(0xffaac9ff),
              ),
            ),
          ],
        ),
      );
    }

    // Present Staff Table
    Widget presentStaffTable() {
      return Card(
        color: Colors.white,
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Present Staff",
                style: GoogleFonts.mulish(
                  fontWeight: FontWeight.bold,
                  fontSize: 22,
                  color: logoTealColor,
                ),
              ),
              const SizedBox(height: 16),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                  headingRowColor: WidgetStateProperty.all(
                    logoTealColor.withOpacity(0.08),
                  ),
                  columns: [
                    DataColumn(
                      columnWidth: presentees.isNotEmpty
                          ? null
                          : FixedColumnWidth(175),
                      label: Row(
                        children: [
                          const Icon(
                            Icons.person,
                            size: 20,
                            color: logoTealColor,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            "Name",
                            style: GoogleFonts.mulish(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    DataColumn(
                      columnWidth: FixedColumnWidth(175),
                      label: Row(
                        children: [
                          const Icon(
                            Icons.access_time,
                            size: 20,
                            color: logoTealColor,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            "Hours Left",
                            style: GoogleFonts.mulish(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    DataColumn(
                      columnWidth: FixedColumnWidth(175),
                      label: Row(
                        children: [
                          const Icon(
                            Icons.timelapse,
                            size: 20,
                            color: logoTealColor,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            "Hours Due",
                            style: GoogleFonts.mulish(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    DataColumn(
                      columnWidth: presentees.isNotEmpty
                          ? null
                          : FixedColumnWidth(175),
                      label: Row(
                        children: [
                          const Icon(
                            Icons.fingerprint,
                            size: 20,
                            color: logoTealColor,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            "Punches",
                            style: GoogleFonts.mulish(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  rows: presentees.map((presentee) {
                    int todayMins = calculateMins(presentee.punchedn);
                    int hoursDue = calculateHoursDue(
                      presentee.userrecord,
                      todayMins,
                      null,
                    );
                    return DataRow(
                      cells: [
                        DataCell(
                          Row(
                            children: [
                              Container(
                                height: 10,
                                width: 10,
                                margin: const EdgeInsets.only(right: 8),
                                decoration: BoxDecoration(
                                  color:
                                      (presentee.punchedn.isNotEmpty &&
                                          presentee.punchedn.last.punchIn)
                                      ? Colors.green
                                      : Colors.red,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              Text(
                                presentee.user.name ?? "",
                                style: GoogleFonts.mulish(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        DataCell(
                          Text(
                            formatMinutesToHourMinute(
                              (480 - presentee.minutesLeft) > 0
                                  ? (480 - presentee.minutesLeft)
                                  : 0,
                            ),
                            style: GoogleFonts.mulish(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        DataCell(
                          Text(
                            formatMinutesToHourMinute(hoursDue),
                            style: GoogleFonts.mulish(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        DataCell(
                          Row(
                            children: [
                              ...List.generate(presentee.punchedn.length, (
                                ind,
                              ) {
                                return Container(
                                  margin: const EdgeInsets.only(right: 16),
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 4,
                                    horizontal: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: presentee.punchedn[ind].punchIn
                                        ? const Color(0xffe6f4ea)
                                        : const Color(0xfffdeaea),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Text(
                                    DateFormat(
                                      'hh:mm aa',
                                    ).format(presentee.punchedn[ind].createdAt),
                                    style: TextStyle(
                                      color: presentee.punchedn[ind].punchIn
                                          ? const Color(0xff3bb273)
                                          : const Color(0xffe57373),
                                      fontWeight: FontWeight.w600,
                                      fontSize: 13,
                                    ),
                                  ),
                                );
                              }),
                            ],
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Absent Staff List
    Widget absentStaffList() {
      final Color accentColor = const Color(0xfff5f6fa);
      final Color labelColor = Colors.grey.shade800;
      final Color chipBgColor = Colors.grey.shade200;
      final Color chipTextColor = Colors.grey.shade800;

      return ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Card(
          color: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Absent Staff",
                  style: GoogleFonts.mulish(
                    fontWeight: FontWeight.bold,
                    fontSize: 22,
                    color: labelColor,
                  ),
                ),
                const SizedBox(height: 12),
                ...List.generate(absentees.length, (index) {
                  int todayMins = calculateMins(
                    widget.hctrl.userTodaydata
                            .firstWhereOrNull(
                              (element) =>
                                  element.user.docId ==
                                  absentees[index].user.docId,
                            )
                            ?.userTodayPunches ??
                        [],
                  );
                  int minutesDue = 0;
                  Recordsmodel? record = absentees[index].userrecord;

                  if (record != null) {
                    DateTime lastAttendDate = record.lastAttend;
                    DateTime today = DateTime.now().add(
                      const Duration(days: 1),
                    );
                    minutesDue += record.minutesDue.toInt();
                    List<DateTime> holidays = holidaylist;
                    List<String> leavedates = record.leavedates ?? [];

                    for (
                      DateTime date = lastAttendDate.add(
                        const Duration(days: 1),
                      );
                      date.isBefore(today);
                      date = date.add(const Duration(days: 1))
                    ) {
                      if (date.year == today.year) {
                        bool isSunday = date.weekday == DateTime.sunday;
                        bool isHoliday = holidays.any(
                          (h) =>
                              h.year == date.year &&
                              h.month == date.month &&
                              h.day == date.day,
                        );
                        String dateStr =
                            "${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}";
                        bool isLeave = leavedates.contains(dateStr);

                        if (!isSunday && !isHoliday && !isLeave) {
                          minutesDue += 480;
                        }
                      }
                    }
                  }

                  int hoursDue = calculateHoursDue(
                    record,
                    todayMins,
                    minutesDue,
                  );
                  return Container(
                    margin: const EdgeInsets.symmetric(vertical: 8.0),
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    decoration: BoxDecoration(
                      color: accentColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.person_2_outlined, color: Colors.redAccent),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            absentees[index].user.name ?? "",
                            style: GoogleFonts.mulish(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: labelColor,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 4,
                            horizontal: 12,
                          ),
                          decoration: BoxDecoration(
                            color: chipBgColor,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Text(
                            formatMinutesToHourMinute(hoursDue),
                            style: GoogleFonts.mulish(
                              fontWeight: FontWeight.bold,
                              color: chipTextColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
                if (absentees.isEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Center(
                      child: Text(
                        "No absentees today!",
                        style: GoogleFonts.mulish(
                          fontSize: 16,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 32, horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          dashboardHeader(),
          isLoading
              ? const Center(child: CircularProgressIndicator())
              : presentStaffTable(),
          const SizedBox(height: 32),
          absentStaffList(),
        ],
      ),
    );
  }
}

// InfoCard widget for dashboard header
class _InfoCard extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color color;

  const _InfoCard({
    required this.icon,
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: color.withOpacity(0.18),
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 18),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(10),
              ),
              padding: const EdgeInsets.all(12),
              child: Icon(icon, color: Colors.white, size: 28),
            ),
            const SizedBox(width: 18),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: GoogleFonts.mulish(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: GoogleFonts.mulish(
                    fontWeight: FontWeight.bold,
                    fontSize: 22,
                    color: logoTealColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
