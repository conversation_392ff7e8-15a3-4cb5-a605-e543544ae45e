import 'package:flutter/material.dart';

bool testMode = false;

const orangeColor = Color(0xFFEE8023);

const logoTealColor = Color(0xFF36748A);

const greenColor = Color.fromARGB(255, 106, 165, 107);

const whiteColor = Colors.white;

const redColor = Colors.red;

const popupBgColor = Colors.white;

final Map<String, String> inputTypeMap = {
  "Yes/No": "yesno",
  "Textfield": "textfield",
};

const String admin = 'HR , Admin';
const String employee = 'Employee';
const String accounts = 'Accounts';
const String mts = 'Manager , TL , Supervisor';

class Permissions {
  //dashboard
  static const String canSeeDashboardTab = 'Can See Dashboard';

  //user
  static const String canAddUser = 'Can Add User';
  static const String canEditUser = 'Can Edit User';
  static const String canDeleteUser = 'Can Delete User';
  static const String canSeeUsersTab = 'Can See Users Tab';
  static const String canSeeUsersDetails = 'Can See Users Details';

  //customer
  static const String canAddCustomer = 'Can Add Customer';
  static const String canEditCustomer = 'Can Edit Customer';
  static const String canDeleteCustomer = 'Can Delete Customer';
  static const String canSeeCustomersTab = 'Can See Customers Tab';
  static const String canSeeCustomersDetails = 'Can See Customers Details';

  //settings
  static const String canSeeSettingsTab = 'Can See Settings Tab';
  static const String canCreateMasterTasks = 'Can Create Master Tasks';
  static const String canCreateMasterQC = 'Can Create Master QC';
  static const String canCreateRole = 'Can Create Role';

  //task
  static const String canSeeTasksTab = 'Can See Tasks Tab';
  static const String canAddTask = 'Can Add Task';
  static const String canEditTask = 'Can Edit Task';
  static const String canDeleteTask = 'Can Delete Task';
  static const String canSeeAllTasks = 'Can See All Tasks';
  // static const String canSeeOnlyOwnTasks = 'Can See Only Own Tasks';

  //punch in

  static const String canPunchIn = 'Can Punch';
  static const String canAcceptPunchOutRequest = 'Can Accept Punch Out Request';
  static const String canSeeAttendanceDashboard =
      'Can See Attendance Dashboard';

  static const List<String> all = [
    canSeeDashboardTab,
    canPunchIn,
    canAcceptPunchOutRequest,
    canSeeAttendanceDashboard,
    //
    canAddUser,
    canEditUser,
    canDeleteUser,
    canSeeUsersTab,
    canSeeUsersDetails,

    //
    canSeeTasksTab,
    canAddTask,
    canEditTask,
    canDeleteTask,
    canSeeAllTasks,
    // canSeeOnlyOwnTasks,
    //
    canSeeCustomersTab,
    canAddCustomer,
    canEditCustomer,
    canDeleteCustomer,
    canSeeCustomersDetails,
    //
    canSeeSettingsTab,
    canCreateMasterTasks,
    canCreateMasterQC,
    canCreateRole,

    //
  ];
}

// const clientType = ['Half-Time', 'Full-Time', 'Turnkey'];
class CustomerType {
  static const String halfTime = 'Half-Time';
  static const String fullTime = 'Full-Time';
  static const String turnkey = 'Turnkey';

  static const List<String> all = [halfTime, fullTime, turnkey];
}

class CustomerTimezone {
  static const String est = 'EST';
  static const String cst = 'CST';
  static const String mst = 'MST';
  static const String pst = 'PST';

  static const List<String> all = [est, cst, mst, pst];
}

class CustomerStatus {
  static const String active = 'active';
  static const String inactive = 'inactive';
  static const String pending = 'pending';

  static const List<String> all = [active, inactive, pending];
}
// const role = ['admin', 'manager', 'employee'];

// const tasks = ['', 'a', 'b'];

const shift = ['Morning', 'Evening', 'Night'];

class TaskStatus {
  static const String notYetStarted = 'Not yet started';
  static const String inProgress = 'In progress';
  static const String needDiscussion = 'Need discussion';
  static const String delayed = 'Delayed';
  static const String onHold = 'On hold';
  static const String completed = 'Completed';
  static const String cancelled = 'Cancelled';

  // Optional: list of all statuses
  static const List<String> allStatus = [
    notYetStarted,
    inProgress,
    needDiscussion,
    delayed,
    onHold,
    completed,
    cancelled,
  ];
}

class UserStatus {
  static const String active = 'Active';
  static const String inactive = 'Inactive';

  static const List<String> all = [active, inactive];
}

const reportSentStatus = ['Select Report Sent Status', 'Yes', 'No'];
const customerCallStatus = ['Select Customer Call Status', 'Yes', 'No'];
const callSummary = ['Select Call Summary', 'Yes', 'No'];

const List<String> inputType = ['Yesnomodel', 'Textfieldmodel'];

const taskPriority = ['low', 'medium', 'high'];

final List<DateTime> holidaylist = [
  DateTime(2025, 1, 14), // 14 Jan
  DateTime(2025, 1, 26), // 26 Jan
  DateTime(2025, 3, 31), // 31 March
  DateTime(2025, 6, 7), // 7 June
  DateTime(2025, 6, 27), // 27 June
  DateTime(2025, 8, 9), // 9 Aug
  DateTime(2025, 8, 15), // 15 Aug
  DateTime(2025, 10, 20), // 20 Oct
];
