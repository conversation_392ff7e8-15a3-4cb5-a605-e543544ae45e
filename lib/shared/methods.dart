import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/models/punchmodel.dart';
import 'package:legacy_pms/models/recordsmodel.dart';
import 'package:legacy_pms/models/taskmodel.dart';
import 'package:legacy_pms/models/usermodel.dart';
import 'package:legacy_pms/models/customermodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/firebase.dart';

bool isLoggedIn() =>
    // true;
    FBAuth.auth.currentUser != null;

final GlobalKey<ScaffoldMessengerState> snackbarKey =
    GlobalKey<ScaffoldMessengerState>();

final GlobalKey<ScaffoldMessengerState> snackbar2Key =
    GlobalKey<ScaffoldMessengerState>();

ScaffoldFeatureController<SnackBar, SnackBarClosedReason>? showSnackBar(
  String message, {
  SnackBarAction? action,
  Duration duration = const Duration(milliseconds: 1800),
}) => snackbarKey.currentState?.showSnackBar(
  SnackBar(
    content: Text(message, style: const TextStyle(color: Colors.white)),
    action: action,
    duration: duration,
  ),
);

ScaffoldFeatureController<SnackBar, SnackBarClosedReason> showCtcAppSnackBar(
  BuildContext context,
  String message, {
  SnackBarAction? action,
  Duration duration = const Duration(milliseconds: 1500),
}) => ScaffoldMessenger.of(context).showSnackBar(
  SnackBar(
    content: Text(message, style: const TextStyle(color: Colors.white)),
    action: action,
    duration: duration,
  ),
);

String capitalizeFirstLetter(String s) {
  if (s.isEmpty) return s;
  return s[0].toUpperCase() + s.substring(1);
}

String generateRandomId(int length) {
  const chars = '0123456789';
  Random rand = Random();

  String randomId = List.generate(
    length,
    (_) => chars[rand.nextInt(chars.length)],
  ).join();

  return randomId;
}

Future<List<String>> fetchCombinedRoleTitles() async {
  final snapshot = await FBFireStore.roles.get();
  return snapshot.docs.map((doc) => (doc.data())['title'] as String).toList();
}

String formatMinutesToHourMinute(int totalMinutes) {
  int hours = totalMinutes ~/ 60;
  int minutes = totalMinutes % 60;

  return '$hours h & ${minutes.toString().padLeft(2, '0')} m';
}

int calculateHoursDue(Recordsmodel? record, int todayMins, int? minutesDue) {
  int totalAttendance = (record?.totalAttendance ?? 0) > 0
      ? (record?.totalAttendance ?? 0) - 1
      : 0;
  int totalMinutes = record?.totalMinutes ?? 0;
  int minutesDueAbsent = (record?.minutesDue ?? 0).toInt();

  int hoursDue =
      ((totalAttendance * 60 * 8) -
      (totalMinutes - todayMins) +
      minutesDueAbsent +
      (minutesDue ?? 0));

  return hoursDue;
}

/// Fetches all punches for a given user on a specific date (one-time fetch).
Future<List<Punchmodel>> fetchUserPunchesForDate(
  DateTime date,
  String userId,
) async {
  final startOfDay = DateTime(date.year, date.month, date.day, 0, 0, 0);
  final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59, 999);

  final snapshot = await FBFireStore.punches
      .where('uId', isEqualTo: userId)
      .where('createdAt', isGreaterThanOrEqualTo: startOfDay)
      .where('createdAt', isLessThanOrEqualTo: endOfDay)
      .get();

  return snapshot.docs.map((e) => Punchmodel.fromSnap(e)).toList();
}

class CustomCard extends StatefulWidget {
  CustomCard({
    super.key,
    required this.iconn,
    required this.texxt,
    required this.dataTexxt,
    required this.color,
    this.extraTexxt,
  });

  final IconData? iconn;
  final String texxt;
  final String dataTexxt;
  final Color color;
  String? extraTexxt;

  @override
  State<CustomCard> createState() => _CustomCardState();
}

class _CustomCardState extends State<CustomCard> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 170,
      width: 150,
      child: Card(
        color: widget.color,
        elevation: 0,
        shape: ContinuousRectangleBorder(
          borderRadius: BorderRadius.circular(28),
        ),
        child: Padding(
          padding: const EdgeInsets.all(13.0),
          child: Column(
            children: [
              const SizedBox(width: 5),
              Align(
                alignment: Alignment.topLeft,
                child: Row(
                  children: [
                    Icon(widget.iconn),
                    const SizedBox(width: 5),
                    Text(
                      widget.texxt,
                      // "Total Attendance :",
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Center(
                child: Text(
                  widget.dataTexxt,
                  // "${ctrl.record?.totalAttendance ?? 0}",
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 32,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              Text(widget.extraTexxt ?? ""),
            ],
          ),
        ),
      ),
    );
  }
}

int calculateMins(List<Punchmodel> punches) {
  if (punches.isEmpty) {
    return 0;
  }

  punches.sort((a, b) => a.createdAt.compareTo(b.createdAt));

  int totalMins = 0;

  for (int i = 0; i < punches.length - 1; i += 2) {
    if (punches[i].punchIn && !punches[i + 1].punchIn) {
      totalMins += punches[i + 1].createdAt
          .difference(punches[i].createdAt)
          .inMinutes;
    } else {}
  }

  return totalMins;
}

Future<void> punchIn(HomeCtrl ctrl) async {
  // processingPunch = true;
  // setState(() {});

  try {
    print("punchIn: Transaction started");
    await FBFireStore.fb.runTransaction((transaction) async {
      final now = DateTime.now();
      final userId = FirebaseAuth.instance.currentUser?.uid;
      print("punchIn: userId = $userId, now = $now");
      if (userId == null) throw Exception("User not found");

      // Query for this month's record for the user
      final recordsQuery = FBFireStore.records
          .where("createdAt", isEqualTo: DateTime(now.year, now.month, 1))
          .where("uId", isEqualTo: userId);

      final querySnapshot = await recordsQuery.get();
      print("punchIn: recordsQuery.docs.length = ${querySnapshot.docs.length}");

      if (querySnapshot.docs.isNotEmpty) {
        // Update existing record
        final sfDocRef = FBFireStore.records.doc(querySnapshot.docs.first.id);
        final recordData = await transaction.get(sfDocRef);

        if (recordData.exists) {
          final recordMap = recordData.data() ?? {};
          final lastUpdated = recordMap["lastAttend"];
          final lastAttendTimestamp = recordMap["lastAttend"];
          DateTime lastAttendDate;
          if (lastAttendTimestamp is Timestamp) {
            lastAttendDate = lastAttendTimestamp.toDate();
          } else if (lastAttendTimestamp is DateTime) {
            lastAttendDate = lastAttendTimestamp;
          } else {
            lastAttendDate = now;
          }

          int minutesDue = recordMap["minutesDue"] ?? 0;
          List<DateTime> holidays = holidaylist;
          List<String> leavedates = [];
          var leavedatesRaw = recordMap["leavedates"];
          if (leavedatesRaw is List) {
            leavedates = leavedatesRaw.map((e) => e.toString()).toList();
          }

          print(
            "punchIn: lastAttendDate = $lastAttendDate, holidays = $holidays, leavedates = $leavedates",
          );

          for (
            DateTime date = lastAttendDate.add(const Duration(days: 1));
            date.isBefore(DateTime(now.year, now.month, now.day));
            date = date.add(const Duration(days: 1))
          ) {
            bool isSunday = date.weekday == DateTime.sunday;
            bool isHoliday = holidays.any(
              (h) =>
                  h.year == date.year &&
                  h.month == date.month &&
                  h.day == date.day,
            );
            String dateStr =
                "${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}";
            bool isLeave = leavedates.contains(dateStr);

            print(
              "punchIn: Checking date $dateStr - isSunday: $isSunday, isHoliday: $isHoliday, isLeave: $isLeave",
            );

            if (!isSunday && !isHoliday && !isLeave) {
              minutesDue += 480;
              print(
                "punchIn: Added 480 minutesDue for $dateStr, total now $minutesDue",
              );
            }
          }

          if (minutesDue != (recordMap["minutesDue"] ?? 0)) {
            print("punchIn: Updating minutesDue to $minutesDue");
            transaction.update(sfDocRef, {"minutesDue": minutesDue});
          }

          DateTime? lastUpdatedDate;
          if (lastUpdated != null) {
            lastUpdatedDate = (lastUpdated is Timestamp)
                ? lastUpdated.toDate()
                : lastUpdated;
          }

          print("punchIn: lastUpdatedDate = $lastUpdatedDate");

          if (lastUpdatedDate == null ||
              lastUpdatedDate.day != now.day ||
              lastUpdatedDate.month != now.month ||
              lastUpdatedDate.year != now.year) {
            final newTotalAttendance = now.weekday == DateTime.sunday
                ? recordMap["totalAttendance"]
                : (recordMap["totalAttendance"] ?? 0) + 1;

            print(
              "punchIn: Updating totalAttendance to $newTotalAttendance and lastAttend to $now",
            );
            transaction.update(sfDocRef, {
              "totalAttendance": newTotalAttendance,
              "lastAttend": now,
            });
          }
        }
      } else {
        // No record for this month, create new
        int minutesDue = 0;
        print("punchIn: No record for this month, creating new record");
        await FBFireStore.records.add({
          "totalAttendance": now.weekday == DateTime.sunday ? 0 : 1,
          "totalMinutes": 0,
          "uId": userId,
          "createdAt": DateTime(now.year, now.month, 1),
          "totalLeaves": 0,
          "leavedates": [],
          "minutesDue": minutesDue,
          "lastAttend": DateTime(now.year, now.month, now.day),
        });
      }

      // Add punch-in record
      print("punchIn: Adding punch-in record for userId $userId");
      await FBFireStore.punches.add({
        "punchIn": true,
        "byUser": true,
        "uId": userId,
        "createdAt": Timestamp.now(),
      });
    });
    print("punchIn: Transaction completed");
  } catch (e) {
    debugPrint(e.toString());
    print("punchIn: Exception - $e");
    // } finally {
    //   processingPunch = false;
    //   setState(() {});
  }
}

Future<void> punchOut(HomeCtrl ctrl, String? uId) async {
  try {
    // CALCULATING TOTAL HOURS
    await FBFireStore.fb.runTransaction((transaction) async {
      // Get the user's record for this month
      // final userId = FirebaseAuth.instance.currentUser?.uid;
      if (uId == null) return;

      // Find the user's record for this month
      final now = DateTime.now();
      final recordQuery = await FBFireStore.records
          .where('uId', isEqualTo: uId)
          .where('createdAt', isEqualTo: DateTime(now.year, now.month, 1))
          .limit(1)
          .get();

      if (recordQuery.docs.isEmpty) return;
      final recordDoc = recordQuery.docs.first;
      final sfDocRef = FBFireStore.records.doc(recordDoc.id);
      final recordData = await transaction.get(sfDocRef);

      // Find the last punch-in for this user today
      final punchesQuery = await FBFireStore.punches
          .where('uId', isEqualTo: uId)
          .where('punchIn', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(1)
          .get();

      if (punchesQuery.docs.isEmpty) return;
      final lastPunchInDoc = punchesQuery.docs.first;
      DateTime punchInTime;
      final createdAtValue = lastPunchInDoc.data()['createdAt'];
      if (createdAtValue is Timestamp) {
        punchInTime = createdAtValue.toDate();
      } else if (createdAtValue is DateTime) {
        punchInTime = createdAtValue;
      } else {
        // Try to parse if it's a string (for web/JS interop)
        punchInTime =
            DateTime.tryParse(createdAtValue.toString()) ?? DateTime.now();
      }

      final punchOutTime = DateTime.now();
      final duration = punchOutTime.difference(punchInTime);
      final minutes = duration.inMinutes;

      final totalMinutes = (recordData.get("totalMinutes") ?? 0) as int;
      final newTotalMinutes = (totalMinutes + minutes) > 0
          ? totalMinutes + minutes
          : 0;

      try {
        transaction.update(sfDocRef, {
          "lastAttend": DateTime.now(),
          "totalMinutes": newTotalMinutes,
        });
      } catch (e) {
        debugPrint("Error updating record: $e");
      }
    });

    // CREATE PUNCH OUT
    await FBFireStore.punches.add({
      "punchIn": false,
      "byUser": false,
      "uId": uId,
      "createdAt": DateTime.now(),
    });
  } catch (e) {
    debugPrint(e.toString());
  } finally {
    // processingPunch = false;
    // setState(() {});
  }
}

/// Calculates deadline based on task creation date and actual hours
/// Returns null if actualHours is invalid or empty
DateTime? calculateTaskDeadline(TaskModel task) {
  if (task.actualHours == null || task.actualHours!.isEmpty) {
    return null;
  }

  try {
    // Parse the actualHours string (format: "HH:MM" or "HHMM")
    String hoursStr = task.actualHours!.trim();
    List<String> parts;

    if (hoursStr.contains(':')) {
      parts = hoursStr.split(':');
    } else if (hoursStr.length >= 3) {
      // Handle format like "0230" -> "02:30"
      parts = [hoursStr.substring(0, 2), hoursStr.substring(2)];
    } else {
      return null;
    }

    final hours = int.tryParse(parts[0]) ?? 0;
    final minutes = int.tryParse(parts[1]) ?? 0;

    if (hours == 0 && minutes == 0) {
      return null;
    }

    // Calculate deadline by adding the duration to the creation date
    final duration = Duration(hours: hours, minutes: minutes);
    return task.createdAt.add(duration);
  } catch (e) {
    debugPrint('Error parsing actualHours: $e');
    return null;
  }
}

/// Sends email notifications when a task is created or assigned
/// This function fetches user and customer details and calls the Firebase Function
Future<bool> sendTaskAssignmentEmail({
  required TaskModel task,
  DateTime? deadline,
}) async {
  try {
    // Calculate deadline if not provided
    final calculatedDeadline = deadline ?? calculateTaskDeadline(task);

    // Fetch employee details
    final employeeDoc = await FBFireStore.users.doc(task.employee).get();
    if (!employeeDoc.exists) {
      debugPrint('Employee not found: ${task.employee}');
      return false;
    }
    final employee = Usermodel.fromSnap(employeeDoc);

    // Fetch supervisor (TL) details
    final supervisorDoc = await FBFireStore.users.doc(task.supervisor).get();
    if (!supervisorDoc.exists) {
      debugPrint('Supervisor not found: ${task.supervisor}');
      return false;
    }
    final supervisor = Usermodel.fromSnap(supervisorDoc);

    // Fetch customer details
    final customerDoc = await FBFireStore.customers.doc(task.customer).get();
    if (!customerDoc.exists) {
      debugPrint('Customer not found: ${task.customer}');
      return false;
    }
    final customer = Customermodel.fromSnap(customerDoc);

    // Prepare data for Firebase Function
    final emailData = {
      'taskDetails': task.details,
      'employeeEmail': employee.email,
      'employeeName': employee.name,
      'tlEmail': supervisor.email,
      'tlName': supervisor.name,
      'customerName': customer.name,
      'deadline': calculatedDeadline?.toIso8601String(),
      'actualHours': task.actualHours,
      'taskId': task.docId,
    };

    // Call Firebase Function
    final result = await FBFunctions.ff
        .httpsCallable('sendTaskAssignmentEmail')
        .call(emailData);

    final response = result.data;
    if (response['success'] == true) {
      debugPrint('Task assignment emails sent successfully');
      return true;
    } else {
      debugPrint('Failed to send task assignment emails: ${response['msg']}');
      return false;
    }
  } catch (e) {
    debugPrint('Error sending task assignment email: $e');
    return false;
  }
}

/// Sends task reminder emails based on the specified reminder type
/// reminderType can be '12pm', '3pm', or '4:30pm'
/// Returns a Map with detailed results
Future<Map<String, dynamic>> sendTaskReminderEmailsDetailed({
  required String reminderType,
}) async {
  try {
    // Validate reminder type
    if (!['12pm', '3pm', '4:30pm'].contains(reminderType)) {
      debugPrint('Invalid reminder type: $reminderType');
      return {
        'success': false,
        'message': 'Invalid reminder type: $reminderType',
        'emailsSent': 0,
        'errors': null,
      };
    }

    // Prepare data for Firebase Function
    final reminderData = {'reminderType': reminderType};

    // Call Firebase Function
    final result = await FBFunctions.ff
        .httpsCallable('sendTaskReminderEmails')
        .call(reminderData);

    final response = result.data;
    if (response['success'] == true) {
      debugPrint('Task reminder emails sent successfully: ${response['msg']}');
      if (response['emailsSent'] != null) {
        debugPrint('Emails sent: ${response['emailsSent']}');
      }
      if (response['errors'] != null) {
        debugPrint('Errors: ${response['errors']}');
      }
      return {
        'success': true,
        'message': response['msg'] ?? 'Emails sent successfully',
        'emailsSent': response['emailsSent'] ?? 0,
        'errors': response['errors'],
      };
    } else {
      debugPrint('Failed to send task reminder emails: ${response['msg']}');
      return {
        'success': false,
        'message': response['msg'] ?? 'Failed to send emails',
        'emailsSent': 0,
        'errors': null,
      };
    }
  } catch (e) {
    debugPrint('Error sending task reminder emails: $e');
    return {
      'success': false,
      'message': 'Error: $e',
      'emailsSent': 0,
      'errors': null,
    };
  }
}

/// Sends task reminder emails based on the specified reminder type
/// reminderType can be '12pm', '3pm', or '4:30pm'
/// Returns true if successful, false otherwise (for backward compatibility)
Future<bool> sendTaskReminderEmails({required String reminderType}) async {
  final result = await sendTaskReminderEmailsDetailed(
    reminderType: reminderType,
  );
  return result['success'] == true;
}
