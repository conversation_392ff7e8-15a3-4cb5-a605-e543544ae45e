import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:legacy_pms/shared/const.dart';

class FBAuth {
  static final auth = FirebaseAuth.instance;
}

class FBFireStore {
  static final fb = FirebaseFirestore.instance;

  //Core collections

  static final users = fb.collection(testMode ? 'testUsers' : 'Users');
  static final customers = fb.collection(
    testMode ? 'testCustomers' : 'Customers',
  );
  static final masterTasks = fb.collection(
    testMode ? 'testMasterTasks' : 'MasterTasks',
  );

  static final masterQc = fb.collection(testMode ? 'testMasterQc' : 'MasterQc');
  static final roles = fb.collection(testMode ? 'testRoles' : 'Roles');
  static final textfield = fb.collection(
    testMode ? 'testTextField' : 'TextField',
  );
  static final yesNO = fb.collection(testMode ? 'testYesNO' : 'YesNO');
  static final settings = fb
      .collection(testMode ? 'testSetting' : 'Setting')
      .doc('sets');
  static final tasks = fb.collection(testMode ? 'testTasks' : 'Tasks');
  static final punches = fb.collection(testMode ? 'testPunches' : 'Punches');
  static final records = fb.collection(testMode ? 'testRecords' : 'Records');
  static final requestPunchOut = fb.collection(
    testMode ? 'testRequestPunchOut' : 'RequestPunchOut',
  );

  // Notifications

  static final notifications = fb.collection(
    testMode ? 'testNotifications' : 'Notifications',
  );
  static final customnotifications = fb.collection(
    testMode ? 'testCustomNotifications' : 'CustomNotifications',
  );
}

class FBStorage {
  static final fbstore = FirebaseStorage.instance;
}

class FBFunctions {
  static final ff = FirebaseFunctions.instance;
}
