import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:legacy_pms/views/attendance/attendance_dashboard.dart';
import 'package:legacy_pms/views/customers/customers_page.dart';
import 'package:legacy_pms/views/dashboard/dashboard_screen.dart';
import 'package:legacy_pms/dashboard.dart';
import 'package:legacy_pms/views/login/loginpage.dart';
import 'package:legacy_pms/views/notifications/notifications_page.dart';
import 'package:legacy_pms/views/settings/create_role_page.dart';
import 'package:legacy_pms/views/settings/settings_page.dart';
import 'package:legacy_pms/views/settings/master_tasks_page.dart';
import 'package:legacy_pms/views/settings/master_qc_page.dart';
import 'package:legacy_pms/views/settings/tasks_history_page.dart';
import 'package:legacy_pms/views/worksheet/datagrid_tasks_page.dart';
import 'package:legacy_pms/views/users/user_details_page.dart';
import 'package:legacy_pms/views/users/users_page.dart';
import 'package:legacy_pms/views/customers/customer_details_page.dart';
import 'package:legacy_pms/views/worksheet/tasks_page.dart';
import 'package:legacy_pms/views/worksheet/trial.dart';

// const homeRoute = Routes.dashboard;

final GoRouter appRouter = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: Routes.signin,
  routes: _routes,
  redirect: redirector,
);

Future<String?> redirector(BuildContext context, GoRouterState state) async {
  if (isLoggedIn()) {
    if (state.fullPath == Routes.signin) {
      if (Get.isRegistered<HomeCtrl>()) {
        final ctrl = Get.find<HomeCtrl>();
        Future.delayed(
          const Duration(milliseconds: 10),
        ).then((value) => ctrl.update());

        // Check permissions and redirect accordingly
        if (ctrl.userRoles.contains(Permissions.canSeeDashboardTab)) {
          return Routes.dashboard;
        } else if (ctrl.userRoles.contains(Permissions.canSeeTasksTab)) {
          return Routes.tasks;
        } else if (ctrl.userRoles.contains(Permissions.canSeeUsersTab)) {
          return Routes.users;
        } else if (ctrl.userRoles.contains(Permissions.canSeeCustomersTab)) {
          return Routes.customers;
        } else if (ctrl.userRoles.contains(Permissions.canSeeSettingsTab)) {
          return Routes.settings;
        } else {
          return Routes.tasks; // Fallback
        }
      }
      return Routes.tasks;
    } else {
      if (Get.isRegistered<HomeCtrl>()) Get.find<HomeCtrl>().onInit();
      return null;
    }
  } else {
    return Routes.signin;
  }
}

List<RouteBase> get _routes {
  return <RouteBase>[
    ShellRoute(
      builder: (context, state, child) {
        if (!Get.isRegistered<HomeCtrl>()) Get.put(HomeCtrl());
        return DashboardScreen(child: child);
      },
      routes: [
        GoRoute(
          path: Routes.dashboard,
          pageBuilder: (BuildContext context, GoRouterState state) {
            return NoTransitionPage(child: CommonDashboardScreen());
          },
        ),

        GoRoute(
          path: Routes.users,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: UsersPage()),
        ),
        GoRoute(
          path: Routes.customers,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: CustomersPage()),
        ),
        GoRoute(
          path: Routes.tasks,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: DataGridTasksPage()),
        ),
        GoRoute(
          path: Routes.notications,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: NotificationsPage()),
        ),
        GoRoute(
          path: Routes.settings,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: SettingsPage()),
        ),
        GoRoute(
          path: Routes.mastertasks,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: MasterTasksPage()),
        ),
        GoRoute(
          path: Routes.masterqc,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: MasterQCPage()),
        ),
        GoRoute(
          path: Routes.createrole,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: CreateRolePage()),
        ),
        GoRoute(
          path: Routes.tasksHistory,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: TasksHistoryPage()),
        ),
        GoRoute(
          path: Routes.attendance,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(child: AttendanceDashboard()),
        ),
        GoRoute(
          path: "${Routes.userdetails}/:id",
          pageBuilder: (BuildContext context, GoRouterState state) {
            final id = state.pathParameters['id'];
            return NoTransitionPage(
              child: UserDetailsPage(userdocId: id == 'null' ? null : id),
            );
          },
        ),
        GoRoute(
          path: "${Routes.customerdetails}/:id",
          pageBuilder: (BuildContext context, GoRouterState state) {
            final id = state.pathParameters['id'];
            return NoTransitionPage(
              child: CustomerDetailsPage(customerId: id == 'null' ? null : id),
            );
          },
        ),

        // GoRoute(
        //   path: "${Routes.loanDetails}/:id",
        //   pageBuilder: (BuildContext context, GoRouterState state) {
        //     final id = state.pathParameters['id'];
        //     return NoTransitionPage(
        //       child: LoanDetailsPage(loanId: id == 'null' ? null : id),
        //     );
        //   },
        // ),
      ],
    ),
    GoRoute(
      path: Routes.signin,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: Loginpage()),
    ),
  ];
}

class Routes {
  static const signin = '/signin';
  static const dashboard = '/dashboard';
  static const users = '/users';
  static const customers = '/customers';
  static const notications = '/notifications';
  static const tasks = '/tasks';
  static const settings = '/settings';
  static const mastertasks = '/mastertasks';
  static const masterqc = '/masterqc';
  static const createrole = '/createrole';
  static const userdetails = '/userdetails';
  static const customerdetails = '/customerdetails';
  static const notifications = '/notifications';
  static const tasksHistory = '/taskshistory';
  static const attendance = '/attendance';
}
