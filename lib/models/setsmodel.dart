import 'package:cloud_firestore/cloud_firestore.dart';

class Setsmodel {
  final DateTime startTime;
  final DateTime endTime;
  final DateTime interval;

  Setsmodel({
    required this.startTime,
    required this.endTime,
    required this.interval,
  });

  /// 🔹 Convert Firestore JSON/Map to Setsmodel
  factory Setsmodel.fromJson(Map<String, dynamic> json) {
    return Setsmodel(
      startTime: (json['startTime'] is Timestamp)
          ? (json['startTime'] as Timestamp).toDate()
          : DateTime.tryParse(json['startTime']?.toString() ?? '') ??
                DateTime.now(),
      endTime: (json['endTime'] is Timestamp)
          ? (json['endTime'] as Timestamp).toDate()
          : DateTime.tryParse(json['endTime']?.toString() ?? '') ??
                DateTime.now(),
      interval: (json['interval'] is Timestamp)
          ? (json['interval'] as Timestamp).toDate()
          : DateTime.tryParse(json['interval']?.toString() ?? '') ??
                DateTime.now(),
    );
  }

  /// 🔹 Convert Setsmodel to Firestore-compatible JSON
  Map<String, dynamic> toJson() {
    return {
      'startTime': Timestamp.fromDate(startTime),
      'endTime': Timestamp.fromDate(endTime),
      'interval': Timestamp.fromDate(interval),
    };
  }

  /// 🔹 Create Setsmodel from DocumentSnapshot (if stored as its own doc)
  factory Setsmodel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return Setsmodel.fromJson(data);
  }

  /// 🔹 Convert Setsmodel to Firestore Map (alias of toJson)
  Map<String, dynamic> toSnap() {
    return {
      'startTime': Timestamp.fromDate(startTime),
      'endTime': Timestamp.fromDate(endTime),
      'interval': Timestamp.fromDate(interval),
    };
  }
}
