import 'package:cloud_firestore/cloud_firestore.dart';

class Recordsmodel {
  final String docId;
  final String uID;
  final DateTime createdAt;
  final int? totalAttendance;
  final int? totalMinutes;
  final int? totalLeaves;
  final DateTime lastAttend;
  final List<String>? leavedates;
  final num minutesDue;

  Recordsmodel({
    required this.leavedates,
    required this.minutesDue,
    required this.docId,
    required this.uID,
    required this.createdAt,
    required this.totalAttendance,
    required this.totalMinutes,
    required this.totalLeaves,
    required this.lastAttend,
  });

  factory Recordsmodel.fromSnap(
    QueryDocumentSnapshot<Map<String, dynamic>> snap,
  ) {
    var data = snap.data();
    return Recordsmodel(
      minutesDue: data['minutesDue'] ?? 0,
      docId: snap.id,
      uID: data['uId'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      totalAttendance: data['totalAttendance'],
      totalMinutes: data['totalMinutes'],
      totalLeaves: data['totalLeaves'],
      lastAttend: (data['lastAttend'] as Timestamp).toDate(),
      leavedates: data['leavedates'] != null
          ? List<String>.from(data['leavedates'])
          : [],
    );
  }

  Map<String, dynamic> toSnap() {
    return {
      'docId': docId,
      'uID': uID,
      'minutesDue': minutesDue,
      'createdAt': Timestamp.fromDate(createdAt),
      'totalAttendance': totalAttendance,
      'totalMinutes': totalMinutes,
      'totalLeaves': totalLeaves,
      'lastAttend': Timestamp.fromDate(lastAttend),
      'leavedates': leavedates ?? [],
    };
  }
}
