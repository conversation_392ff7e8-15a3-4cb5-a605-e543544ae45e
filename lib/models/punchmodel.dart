import 'package:cloud_firestore/cloud_firestore.dart';

class Punchmodel {
  final String docId;
  final DateTime createdAt;
  final String uId;
  final bool punchIn;

  Punchmodel({
    required this.docId,
    required this.createdAt,
    required this.uId,
    required this.punchIn,
  });

  /// 🔹 Convert Firestore JSON/Map to Punchmodel
  factory Punchmodel.fromJson(Map<String, dynamic> json, {String? docId}) {
    return Punchmodel(
      docId: docId ?? json['docId'] ?? '',
      createdAt: (json['createdAt'] is Timestamp)
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
                DateTime.now(),
      uId: json['uId'] ?? '',
      punchIn: json['punchIn'] ?? false,
    );
  }

  /// 🔹 Convert Punchmodel to Firestore-compatible JSON
  Map<String, dynamic> toJson() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'uId': uId,
      'punchIn': punchIn,
      // Optional: include docId in Firestore data if required
      'docId': docId,
    };
  }

  /// 🔹 Create Punchmodel directly from Firestore DocumentSnapshot
  factory Punchmodel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return Punchmodel.fromJson(data, docId: snap.id);
  }

  /// 🔹 Convert to Firestore Map (without storing docId again)
  Map<String, dynamic> toSnap() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'uId': uId,
      'punchIn': punchIn,
    };
  }
}
