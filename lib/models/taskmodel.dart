import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:legacy_pms/shared/const.dart';

class TaskModel {
  final String docId;
  final DateTime createdAt;
  final String details;
  final String? linksAndComments;
  final String customer;
  final String employee;
  final String supervisor;
  // final String qualityChecker;
  final String? pType; // Master task ID
  final String? qcFile;
  final String? numOfErrors;
  final String? actualHours;
  final String? status;
  final String? result;
  final String? supportHour;
  final String? reportSentStatus;
  final String? customerCallStatus;
  final String? callSummary;
  final String? priority;
  final bool? completed;
  final DateTime? completedAt;
  final Map<String, dynamic>? answers;

  TaskModel({
    required this.docId,
    required this.createdAt,
    required this.details,
    this.linksAndComments,
    required this.customer,
    required this.employee,
    required this.supervisor,
    // required this.qualityChecker,
    this.pType,
    this.qcFile,
    this.numOfErrors,
    this.actualHours,
    this.status,
    this.result,
    this.supportHour,
    this.reportSentStatus,
    this.customerCallStatus,
    this.priority,
    this.completed,
    this.completedAt,
    this.callSummary,
    this.answers,
  });

  /// Convert model -> JSON (for saving in Firestore)
  Map<String, dynamic> toJson() {
    return {
      "docId": docId,
      "createdAt": Timestamp.fromDate(createdAt),
      "details": details,
      "linksAndComments": linksAndComments,
      "customer": customer,
      "employee": employee,
      "supervisor": supervisor,
      // "qualityChecker": qualityChecker,
      "pType": pType,
      "qcFile": qcFile,
      "numOfErrors": numOfErrors,
      "actualHours": actualHours,
      "status": status,
      "result": result,
      "supportHour": supportHour,
      "reportSentStatus": reportSentStatus,
      "customerCallStatus": customerCallStatus,
      "priority": priority,
      "completed": completed,
      "completedAt": completedAt != null
          ? Timestamp.fromDate(completedAt!)
          : null,
      "callSummary": callSummary,
      "answers": answers,
    };
  }

  /// Create model from JSON (e.g. API or raw data)
  factory TaskModel.fromJson(Map<String, dynamic> json) {
    return TaskModel(
      docId: json["docId"] ?? "",
      createdAt: json["createdAt"] != null
          ? (json["createdAt"] is Timestamp
                ? (json["createdAt"] as Timestamp).toDate()
                : DateTime.tryParse(json["createdAt"].toString()) ??
                      DateTime.now())
          : DateTime.now(),

      details: json["details"] ?? "",
      linksAndComments: json["linksAndComments"],
      customer: json["customer"] ?? "",
      employee: json["employee"] ?? "",
      supervisor: json["supervisor"] ?? "",
      // qualityChecker: json["qualityChecker"] ?? "",
      pType: json["pType"],
      qcFile: json["qcFile"],
      numOfErrors: json["numOfErrors"],
      actualHours: json["actualHours"],
      status: json["status"],
      result: json["result"],
      supportHour: json["supportHour"],
      reportSentStatus: json["reportSentStatus"],
      customerCallStatus: json["customerCallStatus"],
      priority: json["priority"],
      completed: json["completed"],
      completedAt: json["completedAt"] != null
          ? (json["completedAt"] as Timestamp).toDate()
          : null,
      callSummary: json["callSummary"],
      answers: json["answers"] != null
          ? Map<String, dynamic>.from(json["answers"])
          : null,
    );
  }

  /// Convert Firestore snapshot -> model
  factory TaskModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return TaskModel.fromJson({
      ...data,
      "docId": snap.id, // override docId with Firestore document ID
    });
  }

  /// Convert model -> Firestore map (same as toJson, but excludes docId)
  Map<String, dynamic> toSnap() {
    final data = toJson();
    data.remove("docId"); // Firestore’s auto ID is separate
    return data;
  }

  factory TaskModel.empty() {
    final now = DateTime.now();
    return TaskModel(
      docId: '',
      customer: '',
      pType: null,
      details: '',
      employee: '',
      supervisor: '',
      createdAt: now,
      status: TaskStatus.notYetStarted, // Default status for new tasks
      result: '',
      actualHours: '0',
      reportSentStatus: '',
      customerCallStatus: '',
      callSummary: '',
      qcFile: '',
      numOfErrors: '0',
      linksAndComments: '',
    );
  }

  TaskModel copyWith({
    String? docId,
    DateTime? createdAt,
    String? details,
    String? linksAndComments,
    String? customer,
    String? employee,
    String? supervisor,
    String? pType,
    String? qcFile,
    String? numOfErrors,
    String? actualHours,
    String? status,
    String? result,
    String? supportHour,
    String? reportSentStatus,
    String? customerCallStatus,
    String? callSummary,
    String? priority,
    bool? completed,
    DateTime? completedAt,
    Map<String, dynamic>? answers,
  }) {
    return TaskModel(
      docId: docId ?? this.docId,
      createdAt: createdAt ?? this.createdAt,
      details: details ?? this.details,
      linksAndComments: linksAndComments ?? this.linksAndComments,
      customer: customer ?? this.customer,
      employee: employee ?? this.employee,
      supervisor: supervisor ?? this.supervisor,
      pType: pType ?? this.pType,
      qcFile: qcFile ?? this.qcFile,
      numOfErrors: numOfErrors ?? this.numOfErrors,
      actualHours: actualHours ?? this.actualHours,
      status: status ?? this.status,
      result: result ?? this.result,
      supportHour: supportHour ?? this.supportHour,
      reportSentStatus: reportSentStatus ?? this.reportSentStatus,
      customerCallStatus: customerCallStatus ?? this.customerCallStatus,
      callSummary: callSummary ?? this.callSummary,
      priority: priority ?? this.priority,
      completed: completed ?? this.completed,
      completedAt: completedAt ?? this.completedAt,
      answers: answers ?? this.answers,
    );
  }
}
