import 'package:cloud_firestore/cloud_firestore.dart';

class Rolemodel {
  final String docId;
  final String title;
  final DateTime createdAt;
  final List<String> permissions;

  Rolemodel({
    required this.docId,
    required this.title,
    required this.createdAt,
    required this.permissions,
  });

  /// 🔹 Convert Firestore JSON/Map to Rolemodel
  factory Rolemodel.fromJson(Map<String, dynamic> json, {String? docId}) {
    return Rolemodel(
      docId: docId ?? json['docId'] ?? '',
      title: json['title'] ?? '',
      createdAt: (json['createdAt'] is Timestamp)
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
                DateTime.now(),
      permissions:
          (json['permissions'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
    );
  }

  /// 🔹 Convert Rolemodel to Firestore-compatible JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'createdAt': Timestamp.fromDate(createdAt),
      'permissions': permissions,
      // Optional: include docId in data
      'docId': docId,
    };
  }

  /// 🔹 Create Rolemodel directly from Firestore DocumentSnapshot
  factory Rolemodel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return Rolemodel.fromJson(data, docId: snap.id);
  }

  /// 🔹 Convert to Firestore Map (without docId duplication)
  Map<String, dynamic> toSnap() {
    return {
      'title': title,
      'createdAt': Timestamp.fromDate(createdAt),
      'permissions': permissions,
    };
  }
}
