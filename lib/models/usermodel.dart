import 'package:cloud_firestore/cloud_firestore.dart';

class Usermodel {
  final String docId;
  final String role;
  final String name;
  // final List<String>? permissions;
  // final List<Taskmodel>? taskList;
  // final String shift;
  final String email;
  final String password;
  final DateTime createdAt;
  final String? status;
  final String phoneNo;
  final List<String>? customers;
  final String? designation;

  Usermodel({
    required this.docId,
    required this.role,
    required this.name,
    required this.createdAt,
    // required this.permissions,
    // required this.taskList,
    required this.email,
    // required this.shift,
    required this.password,
    required this.phoneNo,
    this.status,
    required this.customers,
    this.designation,
  });

  factory Usermodel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>? ?? {};

    return Usermodel(
      docId: snap.id,
      role: data['role'] ?? '',
      name: data['name'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      // permissions: data['permissions'] != null
      //     ? List<String>.from(data['permissions'])
      //     : null,
      // taskList: data['taskList'] ?? [],

      // != null
      //     ? List<Taskmodel>.from(
      //         (data['taskList'] as List).map(
      //           (e) => Taskmodel.fromJson(e as Map<String, dynamic>),
      //         ),
      //       )
      //     : null,
      email: data['email'] ?? '',
      // shift: data['shift'] ?? '',
      password: data['password'] ?? '',
      phoneNo: data['phoneNo'] ?? 0000000000,
      status: data['status'],
      customers: data['customers'] != null
          ? List<String>.from(data['customers'])
          : null,
      designation: data['designation'] ?? '',
    );
  }

  // Convert Usermodel to JSON map (e.g., for Firestore)
  Map<String, dynamic> toJson() {
    return {
      'role': role,
      'name': name,
      'createdAt': Timestamp.fromDate(createdAt),
      // 'permissions': permissions,
      // 'taskList': taskList ?? [],
      // taskList?.map((e) => e.toJson()).toList(),
      'email': email,
      // 'shift': shift,
      'password': password,
      'phoneNo': phoneNo,
      'status': status, // Optional: include docId in data
      'customers': customers,
      'designation': designation,
    };
  }

  // Create Usermodel from JSON (Map)
  factory Usermodel.fromJson(Map<String, dynamic> json) {
    return Usermodel(
      docId: json['docId'] ?? '',
      role: json['role'] ?? '',
      name: json['name'] ?? '',
      createdAt: json['createdAt'] is Timestamp
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.parse(
              json['createdAt'] ?? DateTime.now().toIso8601String(),
            ),
      // permissions: json['permissions'] != null
      //     ? List<String>.from(json['permissions'])
      //     : null,
      // taskList: json['taskList'] ?? [],

      // json['taskList'] != null
      //     ? List<Taskmodel>.from(
      //         (json['taskList'] as List).map(
      //           (e) => Taskmodel.fromJson(e as Map<String, dynamic>),
      //         ),
      //       )
      //     : null,
      email: json['email'] ?? '',
      // shift: json['shift'] ?? '',
      password: json['password'] ?? '',
      phoneNo: json['phoneNo'] ?? '',
      status: json['status'] ?? '',
      customers: json['customers'] != null
          ? List<String>.from(json['customers'])
          : null,
      designation: json['designation'] ?? '',
    );
  }
  Usermodel copyWith({
    String? role,
    String? name,
    DateTime? createdAt,
    String? email,
    String? password,
    String? phoneNo,
    String? status,
    List<String>? customers,
    String? designation,
  }) {
    return Usermodel(
      docId: docId,
      role: role ?? this.role,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
      email: email ?? this.email,
      password: password ?? this.password,
      phoneNo: phoneNo ?? this.phoneNo,
      status: status ?? this.status,
      customers: customers ?? this.customers,
      designation: designation ?? this.designation,
    );
  }
}
