import 'package:cloud_firestore/cloud_firestore.dart';

class Yesnomodel {
  final String docId;
  final DateTime createdAt;
  final bool yes;
  final String title;
  final String type;
  final bool? completed;

  Yesnomodel({
    required this.docId,
    required this.createdAt,
    required this.yes,
    required this.title,
    required this.type,
    this.completed,
  });

  /// 🔹 Convert Firestore JSON/Map to Yesnomodel
  factory Yesnomodel.fromJson(Map<String, dynamic> json, {String? docId}) {
    return Yesnomodel(
      docId: docId ?? json['docId'] ?? '',
      createdAt: (json['createdAt'] is Timestamp)
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
                DateTime.now(),
      yes: json['yes'] ?? false,
      title: json['title'] ?? '',
      type: json['type'] ?? '',
      completed: json['completed'],
    );
  }

  /// 🔹 Convert Yesnomodel to Firestore-compatible JSON/Map
  Map<String, dynamic> toJson() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'yes': yes,
      'title': title,
      'type': type,
      'completed': completed,
      // Optional: include docId in Firestore data if required
      'docId': docId,
    };
  }

  /// 🔹 Create Yesnomodel from Firestore DocumentSnapshot
  factory Yesnomodel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return Yesnomodel.fromJson(data, docId: snap.id);
  }

  /// 🔹 Convert Yesnomodel to Firestore Map (without redundant docId)
  Map<String, dynamic> toSnap() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'yes': yes,
      'title': title,
      'type': type,
      'completed': completed,
    };
  }

  Yesnomodel copyWith({
    String? docId,
    DateTime? createdAt,
    bool? yes,
    String? title,
    String? type,
    bool? completed,
  }) {
    return Yesnomodel(
      docId: docId ?? this.docId,
      createdAt: createdAt ?? this.createdAt,
      yes: yes ?? this.yes,
      title: title ?? this.title,
      type: type ?? this.type,
      completed: completed ?? this.completed,
    );
  }
}
