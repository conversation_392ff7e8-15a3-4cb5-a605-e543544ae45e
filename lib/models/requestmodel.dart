import 'package:cloud_firestore/cloud_firestore.dart';

class Requestmodel {
  final String docId;
  final DateTime createdAt;
  final String uId;
  final DateTime requestTime;
  final bool active;

  Requestmodel({
    required this.docId,
    required this.createdAt,
    required this.uId,
    required this.requestTime,
    required this.active,
  });

  /// 🔹 Convert Firestore JSON/Map to Requestmodel
  factory Requestmodel.fromJson(Map<String, dynamic> json, {String? docId}) {
    return Requestmodel(
      docId: docId ?? json['docId'] ?? '',
      createdAt: (json['createdAt'] is Timestamp)
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
                DateTime.now(),
      uId: json['uId'] ?? '',
      requestTime: (json['requestTime'] is Timestamp)
          ? (json['requestTime'] as Timestamp).toDate()
          : DateTime.tryParse(json['requestTime']?.toString() ?? '') ??
                DateTime.now(),
      active: json['active'] ?? false,
    );
  }

  /// 🔹 Convert Requestmodel to Firestore-compatible JSON
  Map<String, dynamic> toJson() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'uId': uId,
      'requestTime': Timestamp.fromDate(requestTime),
      'active': active,
      // Optional: include docId if you want it mirrored inside doc data
      'docId': docId,
    };
  }

  /// 🔹 Create Requestmodel directly from Firestore DocumentSnapshot
  factory Requestmodel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return Requestmodel.fromJson(data, docId: snap.id);
  }

  /// 🔹 Convert to Firestore Map (without docId duplication)
  Map<String, dynamic> toSnap() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'uId': uId,
      'requestTime': Timestamp.fromDate(requestTime),
      'active': active,
    };
  }
}
