import 'package:cloud_firestore/cloud_firestore.dart';

class Notificationmodel {
  final String docId;
  final String title;
  final String desc;
  final String topic;
  final DateTime createdAt;
  final bool test;

  Notificationmodel({
    required this.docId,
    required this.title,
    required this.desc,
    required this.topic,
    required this.createdAt,
    required this.test,
  });

  // From JSON
  factory Notificationmodel.fromJson(Map<String, dynamic> json) {
    return Notificationmodel(
      docId: json['docId'] ?? '',
      title: json['title'] ?? '',
      desc: json['desc'] ?? '',
      topic: json['topic'] ?? '',
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      test: json['test'] ?? false,
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'title': title,
      'desc': desc,
      'topic': topic,
      'createdAt': Timestamp.fromDate(createdAt),
      'test': test,
    };
  }

  // From Firestore DocumentSnapshot
  factory Notificationmodel.fromSnap(DocumentSnapshot snap) {
    var data = snap.data() as Map<String, dynamic>;
    return Notificationmodel(
      docId: snap.id,
      title: data['title'] ?? '',
      desc: data['desc'] ?? '',
      topic: data['topic'] ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      test: data['test'] ?? false,
    );
  }

  // To Firestore format (without docId)
  Map<String, dynamic> toSnap() {
    return {
      'title': title,
      'desc': desc,
      'topic': topic,
      'createdAt': Timestamp.fromDate(createdAt),
      'test': test,
    };
  }
}
