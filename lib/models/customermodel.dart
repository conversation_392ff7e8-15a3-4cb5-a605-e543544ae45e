import 'package:cloud_firestore/cloud_firestore.dart';

class Customermodel {
  final String docId;
  final DateTime createdAt;
  final String name;
  final String clientType; // halftime or fulltime
  final String? status;
  final String? phoneNo;
  final String companyName;
  final String? email;
  final String? timeZone;
  final List<String>? masterTasks;

  Customermodel({
    required this.docId,
    required this.createdAt,
    required this.name,
    required this.clientType,
    this.email,
    this.phoneNo,
    this.status,
    required this.companyName,
    this.timeZone,
    this.masterTasks,
  });

  // 🔹 Convert Firestore JSON/Map to Model
  factory Customermodel.fromJson(Map<String, dynamic> json, {String? docId}) {
    return Customermodel(
      docId: docId ?? json['docId'] ?? '',
      createdAt: (json['createdAt'] is Timestamp)
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.tryParse(json['createdAt'].toString()) ?? DateTime.now(),
      name: json['name'] ?? '',
      clientType: json['clientType'] ?? '',
      email: json['email'] ?? '',
      phoneNo: json['phoneNo'] ?? '',
      status: json['status'] ?? '',
      companyName: json['companyName'] ?? '',
      timeZone: json['timeZone'] ?? '',
      masterTasks: json['masterTasks'] != null
          ? List<String>.from(json['masterTasks'])
          : null,
    );
  }

  // 🔹 Convert Model to JSON/Map for Firestore
  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'createdAt': createdAt,
      'name': name,
      'clientType': clientType,
      'email': email,
      'phoneNo': phoneNo,
      'status': status,
      'companyName': companyName,
      'timeZone': timeZone,
      'masterTasks': masterTasks,
    };
  }

  // 🔹 Create Model from Firestore Snapshot
  factory Customermodel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return Customermodel.fromJson(data, docId: snap.id);
  }

  // 🔹 Convert to Firestore-compatible Map (with Timestamp)
  Map<String, dynamic> toSnap() {
    return {
      'createdAt': Timestamp.fromDate(createdAt),
      'name': name,
      'clientType': clientType,
      'email': email,
      'phoneNo': phoneNo,
      'status': status,
      'companyName': companyName,
      'timeZone': timeZone,
      'masterTasks': masterTasks,
    };
  }

  Customermodel copyWith({
    String? docId,
    DateTime? createdAt,
    String? name,
    String? clientType,
    String? status,
    String? phoneNo,
    String? companyName,
    String? email,
    String? timeZone,
    List<String>? masterTasks,
  }) {
    return Customermodel(
      docId: docId ?? this.docId,
      createdAt: createdAt ?? this.createdAt,
      name: name ?? this.name,
      clientType: clientType ?? this.clientType,
      status: status ?? this.status,
      phoneNo: phoneNo ?? this.phoneNo,
      companyName: companyName ?? this.companyName,
      email: email ?? this.email,
      timeZone: timeZone ?? this.timeZone,
      masterTasks: masterTasks ?? this.masterTasks,
    );
  }
}
