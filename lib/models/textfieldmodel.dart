import 'package:cloud_firestore/cloud_firestore.dart';

class Textfieldmodel {
  final String docId;
  final String title;
  final DateTime createdAt;
  final String type;
  final String? answer;
  final bool? completed;

  Textfieldmodel({
    required this.docId,
    required this.title,
    required this.createdAt,
    required this.type,
    this.answer,
    this.completed,
  });

  /// 🔹 Convert Firestore JSON/Map to Textfieldmodel
  factory Textfieldmodel.fromJson(Map<String, dynamic> json, {String? docId}) {
    return Textfieldmodel(
      docId: docId ?? json['docId'] ?? '',
      title: json['title'] ?? '',
      createdAt: (json['createdAt'] is Timestamp)
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
                DateTime.now(),
      type: json['type'] ?? '',
      answer: json['answer'],
      completed: json['completed'],
    );
  }

  /// 🔹 Convert Textfieldmodel to Firestore-compatible JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'createdAt': Timestamp.fromDate(createdAt),
      'type': type,
      'answer': answer,
      'completed': completed,
      // Optional: include docId in Firestore data if you need mirror copy
      'docId': docId,
    };
  }

  /// 🔹 Create Textfieldmodel directly from Firestore DocumentSnapshot
  factory Textfieldmodel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return Textfieldmodel.fromJson(data, docId: snap.id);
  }

  /// 🔹 Convert to Firestore Map (without docId duplication)
  Map<String, dynamic> toSnap() {
    return {
      'title': title,
      'createdAt': Timestamp.fromDate(createdAt),
      'type': type,
      'answer': answer,
      'completed': completed,
    };
  }

  Textfieldmodel copyWith({
    String? docId,
    String? title,
    DateTime? createdAt,
    String? type,
    String? answer,
    bool? completed,
  }) {
    return Textfieldmodel(
      docId: docId ?? this.docId,
      title: title ?? this.title,
      createdAt: createdAt ?? this.createdAt,
      type: type ?? this.type,
      answer: answer ?? this.answer,
      completed: completed ?? this.completed,
    );
  }
}
