import 'package:cloud_firestore/cloud_firestore.dart';

class Masterqcmodel {
  final String docId;
  final String title;
  final DateTime createdAt;
  final List?
  qcInputModel; // Could be List<Map<String, dynamic>> or List<String>, etc.

  Masterqcmodel({
    required this.docId,
    required this.title,
    required this.createdAt,
    required this.qcInputModel,
  });

  /// 🔹 Convert Firestore JSON/Map to Model
  factory Masterqcmodel.fromJson(Map<String, dynamic> json, {String? docId}) {
    return Masterqcmodel(
      docId: docId ?? json['docId'] ?? '',
      title: json['title'] ?? '',
      createdAt: (json['createdAt'] is Timestamp)
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
                DateTime.now(),
      qcInputModel:
          json['qcInputModel'], // Already a list / you can cast if needed
    );
  }

  /// 🔹 Convert Model to JSON/Map for Firestore
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'createdAt': Timestamp.fromDate(createdAt),
      'qcInputModel': qcInputModel,
      // Optional: include docId inside data if you need mirror
      'docId': docId,
    };
  }

  /// 🔹 Create Model from Firestore DocumentSnapshot
  factory Masterqcmodel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return Masterqcmodel.fromJson(data, docId: snap.id);
  }

  /// 🔹 Convert to Firestore-compatible Map (alias for toJson without docId duplication)
  Map<String, dynamic> toSnap() {
    return {
      'title': title,
      'createdAt': Timestamp.fromDate(createdAt),
      'qcInputModel': qcInputModel,
    };
  }
}
