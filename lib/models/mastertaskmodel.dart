import 'package:cloud_firestore/cloud_firestore.dart';

class Mastertaskmodel {
  final String docId;
  final String title;
  final String desc;
  final DateTime createdAt;
  final String type; // d/w/m
  final String benchmark;
  bool showCount;
  final String masterQc;

  Mastertaskmodel({
    required this.docId,
    required this.title,
    required this.desc,
    required this.createdAt,
    required this.type,
    required this.benchmark,
    required this.showCount,
    required this.masterQc,
  });

  /// 🔹 Convert Firestore JSON/Map to Model
  factory Mastertaskmodel.fromJson(Map<String, dynamic> json, {String? docId}) {
    return Mastertaskmodel(
      docId: docId ?? json['docId'] ?? '',
      title: json['title'] ?? '',
      desc: json['desc'] ?? '',
      createdAt: (json['createdAt'] is Timestamp)
          ? (json['createdAt'] as Timestamp).toDate()
          : DateTime.tryParse(json['createdAt']?.toString() ?? '') ??
                DateTime.now(),
      type: json['type'] ?? '',
      benchmark: json['benchmark'] ?? '',
      showCount: json['showCount'] ?? false,
      masterQc: json['masterQc'] ?? '',
    );
  }

  /// 🔹 Convert Model to Firestore-compatible JSON/Map
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'desc': desc,
      'createdAt': Timestamp.fromDate(createdAt),
      'type': type,
      // Optional: include docId inside data if you want mirror copy
      'docId': docId,
      'benchmark': benchmark,
      'showCount': showCount,
      'masterQc': masterQc,
    };
  }

  /// 🔹 Create Model directly from Firestore DocumentSnapshot
  factory Mastertaskmodel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return Mastertaskmodel.fromJson(data, docId: snap.id);
  }

  /// 🔹 Convert to Firestore Map (without docId duplication)
  Map<String, dynamic> toSnap() {
    return {
      'title': title,
      'desc': desc,
      'createdAt': Timestamp.fromDate(createdAt),
      'type': type,
      'benchmark': benchmark,
      'showCount': showCount,
      'masterQc': masterQc,
    };
  }
}
