import 'package:cloud_firestore/cloud_firestore.dart';

class Settingsmodel {
  final int requestHour;
  final int requestMin;

  Settingsmodel({required this.requestHour, required this.requestMin});

  /// 🔹 Convert Firestore JSON/Map to Settingsmodel
  factory Settingsmodel.fromJson(Map<String, dynamic> json) {
    return Settingsmodel(
      requestHour: json['requestHour'] ?? 0,
      requestMin: json['requestMin'] ?? 0,
    );
  }

  /// 🔹 Convert Settingsmodel to Firestore-compatible JSON
  Map<String, dynamic> toJson() {
    return {'requestHour': requestHour, 'requestMin': requestMin};
  }

  /// 🔹 Create Settingsmodel directly from Firestore DocumentSnapshot
  factory Settingsmodel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return Settingsmodel.fromJson(data);
  }

  /// 🔹 Convert to Firestore Map (alias of toJson)
  Map<String, dynamic> toSnap() {
    return {'requestHour': requestHour, 'requestMin': requestMin};
  }
}
