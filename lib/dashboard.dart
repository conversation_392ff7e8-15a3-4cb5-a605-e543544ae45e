import 'package:flutter/material.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:legacy_pms/views/drawer/dashboard_drawer.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key, required this.child});
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: snackbar2Key,
      backgroundColor: Colors.white,
      body: Column(
        children: [
          const DashboardDrawer(),
          Expanded(child: child),
          // Expanded(
          //   child: SingleChildScrollView(
          //     child: Column(
          //       children: [
          //         ...List.generate(1000, (index) => Text("Item $index")),
          //       ],
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
