# Task Reminder Email System

This document describes the task reminder email system that sends automated reminders at specific times based on task status.

## Overview

The system sends reminder emails at three different times:
- **12:00 PM**: For tasks that haven't started or need discussion
- **3:00 PM**: For all incomplete tasks (general reminder)
- **4:30 PM**: For all incomplete tasks (delayed reminder)

## Email Types

### 1. 12:00 PM Reminders

#### Not Yet Started Tasks
- **Subject**: `Reminder: Task "[Task Name]" Yet to Start`
- **Recipients**: Employee and TL
- **Condition**: Task status is "Not yet started"

#### Discussion Required Tasks
- **Subject**: `Reminder: Task "[Task Name]" Needs Discussion`
- **Recipients**: Employee and TL
- **Condition**: Task status is "On hold" or "Need discussion"

### 2. 3:00 PM Reminders

#### General Status Update
- **Subject**: `Reminder: Task "[Task Name]" Status Update Required`
- **Recipients**: Employee and TL
- **Condition**: Task status is NOT "Completed"

### 3. 4:30 PM Reminders

#### Delayed Task Alert
- **Subject**: `Delayed Task Reminder: "[Task Name]"`
- **Recipients**: Employee and TL
- **Condition**: Task status is NOT "Completed"

## Implementation

### Firebase Functions

The system uses two main Firebase Functions:

1. **`sendTaskReminderEmails`**: Main function that processes all tasks and sends appropriate reminders
2. **`sendTaskReminderEmail`**: Helper function that sends individual reminder emails

### Flutter Integration

A Flutter function `sendTaskReminderEmails()` is available in `lib/shared/methods.dart`:

```dart
Future<bool> sendTaskReminderEmails({
  required String reminderType, // '12pm', '3pm', or '4:30pm'
}) async
```

### Testing

A test button "Test Reminders" is available in the Tasks page for users with task creation permissions. This button tests all three reminder types.

## Email Templates

All emails include:
- Professional HTML and text formatting
- Task details and customer information
- "View Task" button linking to the PMS
- Consistent branding from "Legacy PMS Team"

## Scheduling

To set up automated scheduling, you would typically use:
- **Cloud Scheduler** (Google Cloud) for production
- **Cron jobs** for server-based scheduling
- **Manual triggers** for testing (as implemented)

## Usage Example

```dart
// Send 12pm reminders
await sendTaskReminderEmails(reminderType: '12pm');

// Send 3pm reminders
await sendTaskReminderEmails(reminderType: '3pm');

// Send 4:30pm reminders
await sendTaskReminderEmails(reminderType: '4:30pm');
```

## Task Filtering

The system only processes tasks that:
- Were created on the current day
- Were created after 11:00 AM (start of work day)
- Have valid employee, supervisor, and customer data

## Error Handling

The system includes comprehensive error handling:
- Individual task processing errors are logged but don't stop the entire process
- Email sending failures are tracked and reported
- Detailed success/failure statistics are returned

## Future Enhancements

Potential improvements:
- Add timezone support for different regions
- Implement email frequency limits (e.g., only one discussion email per day)
- Add more granular status-based filtering
- Include task priority in reminder logic
