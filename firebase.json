{"flutter": {"platforms": {"dart": {"lib/firebase_options.dart": {"projectId": "legacy-pms", "configurations": {"web": "1:667731570274:web:8f851fa85dc0ca9fff2b3b"}}}}}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"]}], "hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}, "storage": {"rules": "storage.rules"}}