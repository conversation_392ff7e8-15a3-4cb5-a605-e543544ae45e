/* import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/models/masterqcmodel.dart';
import 'package:legacy_pms/views/worksheet/qcform_dialog.dart';

void main() {
  group('QcFormDialog Tests', () {
    late Masterqcmodel mockMasterQc;
    late HomeCtrl mockCtrl;

    setUp(() {
      // Create mock data for testing
      mockMasterQc = Masterqcmodel(
        docId: 'test-qc-doc-id',
        title: 'Test QC Form',
        createdAt: DateTime.now(),
        qcInputModel: [
          {'title': 'Test Field 1', 'type': 'textfield'},
          {'title': 'Test Checkbox', 'type': 'yesno'},
          {'title': 'Test Field 2', 'type': 'textfield'},
        ],
      );

      // Create a mock controller (you may need to adjust this based on your HomeCtrl implementation)
      mockCtrl = HomeCtrl();
    });

    testWidgets('Dialog renders correctly with form fields', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => QcFormDialog(
                      masterQc: mockMasterQc,
                      taskDocId: 'test-task-id',
                      userUid: 'test-user-uid',
                      ctrl: mockCtrl,
                      // taskStatus: 'in_progress',
                    ),
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // Tap the button to show the dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Verify dialog is shown
      expect(find.text('TEST QC FORM'), findsOneWidget);

      // Verify form fields are present
      expect(find.text('Test Field 1'), findsOneWidget);
      expect(find.text('Test Checkbox'), findsOneWidget);
      expect(find.text('Test Field 2'), findsOneWidget);

      // Verify buttons are present
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Submit'), findsOneWidget);
      expect(find.text('Send for Rework'), findsOneWidget);

      // Verify text fields are enabled initially
      final textFields = find.byType(TextFormField);
      expect(textFields, findsNWidgets(2)); // Two text fields

      // Verify checkbox is present and enabled
      final checkbox = find.byType(Checkbox);
      expect(checkbox, findsOneWidget);
    });

    testWidgets('Submit button state is correct initially', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => QcFormDialog(
                      masterQc: mockMasterQc,
                      taskDocId: 'test-task-id',
                      userUid: 'test-user-uid',
                      ctrl: mockCtrl,
                      // taskStatus: 'in_progress',
                    ),
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // Show the dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Verify Submit button is initially enabled
      final submitButton = find.text('Submit');
      expect(submitButton, findsOneWidget);

      // Verify the button is enabled (not null onPressed)
      final submitButtonWidget = tester.widget<ElevatedButton>(
        find.ancestor(of: submitButton, matching: find.byType(ElevatedButton)),
      );
      expect(submitButtonWidget.onPressed, isNotNull);

      // Verify Send for Rework button is visible
      expect(find.text('Send for Rework'), findsOneWidget);
    });

    testWidgets('Checkbox state changes correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => QcFormDialog(
                      masterQc: mockMasterQc,
                      taskDocId: 'test-task-id',
                      userUid: 'test-user-uid',
                      ctrl: mockCtrl,
                      // taskStatus: 'in_progress',
                    ),
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // Show the dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Find the checkbox
      final checkbox = find.byType(Checkbox);
      expect(checkbox, findsOneWidget);

      // Verify initial state (should be false/unchecked)
      Checkbox checkboxWidget = tester.widget(checkbox);
      expect(checkboxWidget.value, false);

      // Tap the checkbox
      await tester.tap(checkbox);
      await tester.pump();

      // Verify the checkbox state changed
      checkboxWidget = tester.widget(checkbox);
      expect(checkboxWidget.value, true);

      // Tap again to uncheck
      await tester.tap(checkbox);
      await tester.pump();

      // Verify it's unchecked again
      checkboxWidget = tester.widget(checkbox);
      expect(checkboxWidget.value, false);
    });

    testWidgets('Dialog shows correct buttons based on state', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => QcFormDialog(
                      masterQc: mockMasterQc,
                      taskDocId: 'test-task-id',
                      userUid: 'test-user-uid',
                      ctrl: mockCtrl,
                      // taskStatus: 'in_progress',
                    ),
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // Show the dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Initially should show Cancel, Submit, and Send for Rework buttons
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Submit'), findsOneWidget);
      expect(find.text('Send for Rework'), findsOneWidget);

      // Close button should not be visible initially
      expect(find.text('Close'), findsNothing);
    });

    testWidgets('Text field input works correctly', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => QcFormDialog(
                      masterQc: mockMasterQc,
                      taskDocId: 'test-task-id',
                      userUid: 'test-user-uid',
                      ctrl: mockCtrl,
                      // taskStatus: 'in_progress',
                    ),
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // Show the dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Find the first text field
      final textFields = find.byType(TextFormField);
      expect(textFields, findsNWidgets(2));

      // Enter text in the first field
      await tester.enterText(textFields.first, 'Test input value');
      await tester.pump();

      // Verify the text was entered
      expect(find.text('Test input value'), findsOneWidget);

      // Enter text in the second field
      await tester.enterText(textFields.last, 'Another test value');
      await tester.pump();

      // Verify the second text was entered
      expect(find.text('Another test value'), findsOneWidget);
    });
  });
}
 */
